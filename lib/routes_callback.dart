import 'package:app/app/controllers/global_controller.dart';
import 'package:app/app/modules/company_home/controllers/company_home_controller.dart';
import 'package:app/app/modules/driver_home/controllers/driver_home_controller.dart';
import 'package:app/app/modules/home/<USER>/home_controller.dart';
import 'package:app/app/utils/helper/echo.dart';
import 'package:get/get.dart';

routesCallback(Routing? value) {
  String? previous = value?.previous;
  String? current = value?.current;
  bool isBack = value?.isBack ?? false;
  kEchoRoute('routingCallback previous:$previous current:$current isBack:$isBack');
  if ((current ?? '').contains('/layout')) {
    kEcho('Layout');

    GlobalController globalController = Get.find();
    if (globalController.accountTypeDriver.value) {
      DriverHomeController driverHomeController = Get.find();
      driverHomeController.init(ignoreIfSet :true);
    } else {
      CompanyHomeController companyHomeController = Get.find();
      companyHomeController.init(ignoreIfSet :true);
    }
  }
  if (previous == null || current == null) return;
  if (isBack) {}
}
