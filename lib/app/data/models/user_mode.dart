import 'package:app/app/data/models/general_data_models.dart';

class UserModel {
  final int id;
  final int? driverId;
  final String phone;
  final String name;
  final String userName;
  final String? companyCode;
  final String? email;
  final String? latitude;
  final String? longitude;
  final CityModel? city;
  final String? address;
  final String? image;
  final String? companyName;
  final String? companyType;
  final String? commercialNumber;
  final String? webSite;
  final String? contactPerson;
  final String? phonePerson;
  final String? emailPerson;
  final TruckModel? truck;
  final String? token;
  final String? carLicense;
  final String? carImage;

  UserModel({
    required this.id,
    this.driverId,
    required this.name,
    required this.userName,
    required this.latitude,
    required this.phone,
    required this.city,
    required this.longitude,
    this.companyCode,
    this.email,
    this.address,
    this.image,
    this.companyName,
    this.companyType,
    this.commercialNumber,
    this.webSite,
    this.contactPerson,
    this.phonePerson,
    this.emailPerson,
    this.carImage,
    this.carLicense,
    required this.truck,
    required this.token,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        id: json['id'],
        driverId: json['driverId'],
        name: json['fullName'] ?? json['userName'] ?? '',
        userName: json['userName'],
        companyCode: json['companyCode'],
        email: json['email'],
        phone: json['phone'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        city: json['city'] == null ? null : CityModel.fromJson(json['city']),
        address: json['address'],
        image: json['image'],
        companyName: json['companyName'],
        companyType: json['companyType'],
        commercialNumber: json['commercialNumber'],
        webSite: json['webSite'],
        contactPerson: json['contactPerson'],
        phonePerson: json['phonePerson'],
        emailPerson: json['emailPerson'],
        truck: json['truck'] == null ? TruckModel.tryfromJson(json) : TruckModel.fromJson(json['truck']),
        token: json['access_token'],
        carLicense: json['carLicense'],
        carImage: json['carImage'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'driverId': driverId,
        'fullName': name,
        'userName': userName,
        'companyCode': companyCode,
        'email': email,
        'phone': phone,
        'latitude': latitude,
        'longitude': longitude,
        'city': city?.toJson(),
        'address': address,
        'image': image,
        'companyName': companyName,
        'companyType': companyType,
        'commercialNumber': commercialNumber,
        'webSite': webSite,
        'contactPerson': contactPerson,
        'phonePerson': phonePerson,
        'emailPerson': emailPerson,
        'truck': truck?.toJson(),
        'access_token': token,
      };
}
