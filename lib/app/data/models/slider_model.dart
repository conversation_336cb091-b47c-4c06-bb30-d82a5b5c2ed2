// [
//     {
//         "id": 2,
//         "title": "تيست1",
//         "link": "https://www.google.com",
//         "image": "https://app.hamoulty.com/uploads/image/Slider/6835e8fe09daa.png"
//     },
//     {
//         "id": 1,
//         "title": "تيست",
//         "link": "https://www.google.com",
//         "image": "https://app.hamoulty.com/uploads/image/Slider/6835e8f24c5ed.jpg"
//     }
// ]

class SliderModel {
  SliderModel({
    this.id,
    this.title,
    this.link,
    this.image,
  });

  int? id;
  String? title;
  String? link;
  String? image;

  factory SliderModel.fromJson(Map<String, dynamic> json) => SliderModel(
        id: json["id"],
        title: json["title"],
        link: json["link"],
        image: json["image"],
      );
}