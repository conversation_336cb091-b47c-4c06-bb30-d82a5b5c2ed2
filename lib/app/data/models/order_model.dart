class OrderModel {
  final int? id;
  final int? orderNumber;
  final DateTime? startOrder;
  final DateTime? endOrder;
  final String? weight;
  final int? truckTypeId;
  final String? truckTypeName;
  final int? truckSizeId;
  final String? truckSizeName;
  final String? startLatitude;
  final String? startLongitude;
  final String? startAddress;
  final String? endLatitude;
  final String? endLongitude;
  final String? endAddress;
  final String? trackLatitude;
  final String? trackLongitude;
  final String? trackAddress;
  final String? description;
  final String? notes;
  final int? driverId;
  final String? driverName;
  final int? companyId;
  final String? companyName;
  final int? orderStatusId;
  final String? orderStatusName;
  final String? price;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<OrderTrackingModel>? tracking;
  final List<OrderTrackModel>? orderTrack;

  OrderModel({
    this.id,
    this.orderNumber,
    this.startOrder,
    this.endOrder,
    this.weight,
    this.truckTypeId,
    this.truckTypeName,
    this.truckSizeId,
    this.truckSizeName,
    this.startLatitude,
    this.startLongitude,
    this.startAddress,
    this.endLatitude,
    this.endLongitude,
    this.endAddress,
    this.trackLatitude,
    this.trackLongitude,
    this.trackAddress,
    this.description,
    this.notes,
    this.driverId,
    this.driverName,
    this.companyId,
    this.companyName,
    this.orderStatusId,
    this.orderStatusName,
    this.price,
    this.createdAt,
    this.updatedAt,
    this.tracking,
    this.orderTrack,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      orderNumber: json['orderNum'],
      startOrder: json['startOrder'] != null ? DateTime.parse(json['startOrder']) : null,
      endOrder: json['endOrder'] != null ? DateTime.parse(json['endOrder']) : null,
      weight: json['weight'],
      truckTypeId: json['truck_type_id'],
      truckTypeName: json['truckType'],
      truckSizeId: json['truck_size_id'],
      truckSizeName: json['truckSize'],
      startLatitude: json['start_latitude'],
      startLongitude: json['start_longitude'],
      startAddress: json['start_address'],
      endLatitude: json['end_latitude'],
      endLongitude: json['end_longitude'],
      endAddress: json['end_address'],
      trackLatitude: json['track_latitude'],
      trackLongitude: json['track_longitude'],
      trackAddress: json['track_address'],
      description: json['desc'] ?? json['description'],
      notes: json['notes'],
      driverId: json['driver_id'],
      driverName: json['driver'] ?? json['driver_name'],
      companyId: json['company_id'],
      companyName: json['company_name'],
      orderStatusId: json['order_status_id'],
      orderStatusName: json['orderStatus'],
      price: json['price'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      tracking: json['tracking'] != null
          ? List<OrderTrackingModel>.from(json['tracking'].map((x) => OrderTrackingModel.fromJson(x)))
          : null,
      orderTrack: json['orderTrack'] != null
          ? List<OrderTrackModel>.from(json['orderTrack'].map((x) => OrderTrackModel.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNum': orderNumber,
      'startOrder': startOrder?.toIso8601String(),
      'endOrder': endOrder?.toIso8601String(),
      'weight': weight,
      'truck_type_id': truckTypeId,
      'truckType': truckTypeName,
      'truck_size_id': truckSizeId,
      'truckSize': truckSizeName,
      'start_latitude': startLatitude,
      'start_longitude': startLongitude,
      'start_address': startAddress,
      'end_latitude': endLatitude,
      'end_longitude': endLongitude,
      'end_address': endAddress,
      'track_latitude': trackLatitude,
      'track_longitude': trackLongitude,
      'track_address': trackAddress,
      'description': description,
      'notes': notes,
      'driver_id': driverId,
      'driver_name': driverName,
      'company_id': companyId,
      'company_name': companyName,
      'order_status_id': orderStatusId,
      'orderStatus': orderStatusName,
      'price': price,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'tracking': tracking?.map((x) => x.toJson()).toList(),
      'orderTrack': orderTrack?.map((x) => x.toJson()).toList(),
    };
  }


}

class OrderTrackingModel {
  final int? id;
  final int? orderId;
  final double? trackLatitude;
  final double? trackLongitude;
  final String? trackAddress;
  final DateTime? createdAt;

  OrderTrackingModel({
    this.id,
    this.orderId,
    this.trackLatitude,
    this.trackLongitude,
    this.trackAddress,
    this.createdAt,
  });

  factory OrderTrackingModel.fromJson(Map<String, dynamic> json) {
    return OrderTrackingModel(
      id: json['id'],
      orderId: json['order_id'],
      trackLatitude: json['track_latitude'],
      trackLongitude: json['track_longitude'],
      trackAddress: json['track_address'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'track_latitude': trackLatitude,
      'track_longitude': trackLongitude,
      'track_address': trackAddress,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}

class OrderTrackModel {
  final int? id;
  final String? orderStatus;
  final String? createdAt;

  OrderTrackModel({
    this.id,
    this.orderStatus,
    this.createdAt,
  });

  factory OrderTrackModel.fromJson(Map<String, dynamic> json) {
    return OrderTrackModel(
      id: json['id'],
      orderStatus: json['orderStatus'],
      createdAt: json['created_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderStatus': orderStatus,
      'created_at': createdAt,
    };
  }
}
