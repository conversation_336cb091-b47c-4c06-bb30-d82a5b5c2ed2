import 'dart:io';
import 'package:app/app/data/models/base_response.dart';
import 'package:app/app/data/models/user_mode.dart';
import 'package:app/app/data/network/apis.dart';
import 'package:app/app/data/network/network_setup.dart';
import 'package:app/app/data/shared_pref.dart';
import 'package:app/app/utils/public_methods.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:dio/dio.dart';

Future<UserModel> networkGetCompanyProfile() async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.get(kCompanyProfileApi);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        UserModel profileData = UserModel.fromJson(response.data);

        // Update shared preferences with latest data
        AppPrefs prefs = AppPrefs();
        prefs.setKey(kPrefKeys.name, profileData.name);
        if (profileData.email != null) prefs.setKey(kPrefKeys.email, profileData.email);
        prefs.setKey(kPrefKeys.phone, profileData.phone);

        return profileData;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<String> networkUpdateCompanyProfile({
  required String fullName,
  required String userName,
  String? email,
  required String phone,
  required int cityId,
  String? address,
  required int truckTypeId,
  required int truckSizeId,
  File? image,
  required String companyName,
  required String companyType,
  String? commercialNumber,
  required String contactPerson,
  required String phonePerson,
  required String emailPerson,
  String? webSite,
}) async {
  Dio dio = networkHeaderSetup(true);
  String deviceId = await getInfoDeviceId();

  Map<String, dynamic> formDataMap = {
    'fullName': fullName,
    'userName': userName,
    if (email != null && email.isNotEmpty) 'email': email,
    'phone': phone,
    'city_id': cityId,
    if (address != null && address.isNotEmpty) 'address': address,
    'truck_type_id': truckTypeId,
    'truck_size_id': truckSizeId,
    'companyName': companyName,
    'companyType': companyType,
    if (commercialNumber != null && commercialNumber.isNotEmpty) 'commercialNumber': commercialNumber,
    'contactPerson': contactPerson,
    'phonePerson': phonePerson,
    'emailPerson': emailPerson,
    if (webSite != null && webSite.isNotEmpty) 'webSite': webSite,
    'deviceId': deviceId,
  };

  // Add multipart file if it exists
  if (image != null) {
    formDataMap['image'] = await MultipartFile.fromFile(
      image.path,
      filename: 'company_logo.${image.path.split('.').last}',
    );
  }

  FormData formData = FormData.fromMap(formDataMap);
  try {
    Response response = await dio.post(kCompanyUpdateProfileApi, data: formData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        // Update shared preferences
        AppPrefs prefs = AppPrefs();
        prefs.setKey(kPrefKeys.name, fullName);
        if (email != null && email.isNotEmpty) prefs.setKey(kPrefKeys.email, email);
        prefs.setKey(kPrefKeys.phone, phone);

        return basicResponse.message ?? Strings().profileUpdatedSuccessfully;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<String> networkCompanyChangePassword({
  required String currentPassword,
  required String newPassword,
}) async {
  Dio dio = networkHeaderSetup(true);

  FormData formData = FormData.fromMap({
    'current_password': currentPassword,
    'password': newPassword,
  });

  try {
    Response response = await dio.post(kCompanyChangePasswordApi, data: formData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        return basicResponse.message ?? Strings().passwordChangedSuccessfully;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<bool> networkCompanyLogout() async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.post(kCompanyLogoutApi);
    if (response.statusCode == 200 || response.statusCode == 201) {
      return true;
    }
    return false;
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<bool> networkCompanyDeleteAccount() async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.post(kCompanyDeleteAccountApi);
    if (response.statusCode == 200 || response.statusCode == 201) {
      return true;
    }
    return false;
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}
