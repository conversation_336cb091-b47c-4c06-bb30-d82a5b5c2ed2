import 'package:app/app/data/models/base_response.dart';
import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/data/network/apis.dart';
import 'package:app/app/data/network/network_setup.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:dio/dio.dart';

Future<String> networkCompanyAddOrder({
  required DateTime startOrder,
  required DateTime endOrder,
  required double weight,
  required int truckTypeId,
  required int truckSizeId,
  required double startLatitude,
  required double startLongitude,
  required String startAddress,
  required double endLatitude,
  required double endLongitude,
  required String endAddress,
  String? description,
  String? notes,
  int? driverId,
}) async {
  Dio dio = networkHeaderSetup(true);

  FormData formData = FormData.fromMap({
    'startOrder': startOrder.toIso8601String(),
    'endOrder': endOrder.toIso8601String(),
    'weight': weight,
    'truck_type_id': truckTypeId,
    'truck_size_id': truckSizeId,
    'start_latitude': startLatitude,
    'start_longitude': startLongitude,
    'start_address': startAddress,
    'end_latitude': endLatitude,
    'end_longitude': endLongitude,
    'end_address': endAddress,
    if (description != null && description.isNotEmpty) 'desc': description,
    if (notes != null && notes.isNotEmpty) 'notes': notes,
    if (driverId != null) 'driver_id': driverId,
  });

  try {
    Response response = await dio.post(kCompanyOrdersApi, data: formData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        return basicResponse.message ?? Strings().orderCreatedSuccessfully;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<List<OrderModel>> networkGetCompanyOrders() async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.get(kCompanyOrdersApi);

    if (response.statusCode == 200 || response.statusCode == 201) {
        List<OrderModel> orders = [];
        if (response.data != null) {
          orders = List<OrderModel>.from(
            response.data.map((x) => OrderModel.fromJson(x))
          );
        }
        return orders;
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<OrderModel> networkGetCompanyOrderDetails({required int orderId}) async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.get('$kCompanyOrderDetailsApi/$orderId');

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        OrderModel order = OrderModel.fromJson(response.data);
        return order;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<String> networkCompanyUpdateOrder({
  required int orderId,
  required DateTime startOrder,
  required DateTime endOrder,
  required double weight,
  required int truckTypeId,
  required int truckSizeId,
  required double startLatitude,
  required double startLongitude,
  required String startAddress,
  required double endLatitude,
  required double endLongitude,
  required String endAddress,
  String? description,
  String? notes,
}) async {
  Dio dio = networkHeaderSetup(true);

  FormData formData = FormData.fromMap({
    'startOrder': startOrder.toIso8601String(),
    'endOrder': endOrder.toIso8601String(),
    'weight': weight,
    'truck_type_id': truckTypeId,
    'truck_size_id': truckSizeId,
    'start_latitude': startLatitude,
    'start_longitude': startLongitude,
    'start_address': startAddress,
    'end_latitude': endLatitude,
    'end_longitude': endLongitude,
    'end_address': endAddress,
    if (description != null && description.isNotEmpty) 'desc': description,
    if (notes != null && notes.isNotEmpty) 'notes': notes,
    '_method': 'PUT',
  });

  try {
    Response response = await dio.post('$kCompanyOrderDetailsApi/$orderId', data: formData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        return basicResponse.message ?? Strings().orderUpdatedSuccessfully;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<String> networkCompanyDeleteOrder({required int orderId}) async {
  Dio dio = networkHeaderSetup(true);

  FormData formData = FormData.fromMap({
    '_method': 'Delete',
  });

  try {
    Response response = await dio.post('$kCompanyOrderDetailsApi/$orderId', data: formData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        return basicResponse.message ?? Strings().orderDeletedSuccessfully;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<List<OrderTrackingModel>> networkCompanyTrackOrder({required int orderId}) async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.get('$kCompanyTrackOrderApi/$orderId');

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        List<OrderTrackingModel> tracking = [];
        if (response.data != null) {
          tracking = List<OrderTrackingModel>.from(
            response.data.map((x) => OrderTrackingModel.fromJson(x))
          );
        }
        return tracking;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<List<OrderModel>> networkGetCompanyReceivedOrders() async {
  Dio dio = networkHeaderSetup(true);

  try {
    Response response = await dio.get(kCompanyReceivedOrdersApi);

    if (response.statusCode == 200 || response.statusCode == 201) {
        List<OrderModel> orders = [];
        if (response.data != null) {
          orders = List<OrderModel>.from(
            response.data.map((x) => OrderModel.fromJson(x))
          );
        }
        return orders;
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}

Future<String> networkCompanyAcceptOrder({required int orderId}) async {
  Dio dio = networkHeaderSetup(true);

  FormData formData = FormData.fromMap({
    'orderId': orderId,
  });

  try {
    Response response = await dio.post(kCompanyAcceptOrderApi, data: formData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      BaseResponse basicResponse = BaseResponse.fromJson(response.data);
      if (basicResponse.code == 200 || basicResponse.code == 201) {
        return basicResponse.message ?? Strings().orderAcceptedSuccessfully;
      } else {
        return Future.error(basicResponse.message ?? Strings().someThingWentWrong);
      }
    }
    return Future.error('server');
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}
