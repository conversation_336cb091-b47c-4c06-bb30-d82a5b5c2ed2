import 'package:app/app/data/models/general_data_models.dart';
import 'package:app/app/data/models/slider_model.dart';
import 'package:app/app/data/network/apis.dart';
import 'package:app/app/data/network/network_setup.dart';
import 'package:app/app/data/shared_pref.dart';
import 'package:dio/dio.dart';

Future<List<SliderModel>> networkSlider() async {
  AppPrefs appPrefs = AppPrefs();
  Dio dio = networkHeaderSetup(false);
  try {
    Map<String, dynamic> queryParameters = {};
    queryParameters['user_id'] = appPrefs.getKey(kPrefKeys.userId, defaultValue: '');
    Response response = await dio.get(kSliderApi, queryParameters: queryParameters);

    if (response.statusCode == 200 || response.statusCode == 201) {
      List<SliderModel> list = List<SliderModel>.from(response.data.map((x) => SliderModel.fromJson(x)));
      return list;
    } else {
      return Future.error('server');
    }
  } on DioException catch (error) {
    return Future.error(networkHandleError(error));
  }
}
