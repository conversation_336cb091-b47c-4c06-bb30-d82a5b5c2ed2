import 'package:app/app/controllers/global_controller.dart';
import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/data/models/slider_model.dart';
import 'package:app/app/data/network/repositories/company_orders_repository.dart';
import 'package:app/app/data/network/repositories/slider_repository.dart';
import 'package:app/app/data/shared_pref.dart';
import 'package:app/app/utils/helper/network_error_response_handler.dart';
import 'package:get/get.dart';

class CompanyHomeController extends GetxController {
  AppPrefs prefs = Get.find();
  GlobalController globalController = Get.find();

  final pageLoading = false.obs;
  final error = ''.obs;

  // Company dashboard data
  final totalOrders = 0.obs;
  final activeOrders = 0.obs;
  final completedOrders = 0.obs;
  final totalDrivers = 0.obs;

  final sliderList = RxList<SliderModel>();

  final orders = <OrderModel>[].obs;

  @override
  void onInit() {
    super.onInit();
  }

  init({bool ignoreIfSet = false}) {
    if(ignoreIfSet && sliderList.isNotEmpty && orders.isNotEmpty) return;
    loadDashboardData();
    getSliderData();
    getRecentOrders();
  }

  Future<void> loadDashboardData() async {
    pageLoading.value = true;
    error.value = '';

    try {
      // TODO: Implement API calls to get company dashboard data
      // For now, using mock data
      await Future.delayed(Duration(seconds: 1));

      totalOrders.value = 25;
      activeOrders.value = 8;
      completedOrders.value = 17;
      totalDrivers.value = 12;
    } catch (e) {
      error.value = 'Failed to load dashboard data';
    }

    pageLoading.value = false;
  }

  Future<void> getRecentOrders() async {
    try {
      List<OrderModel> orders = await networkGetCompanyOrders();
      this.orders.value = orders;
    } catch (e) {
      networkResponseErrorHandler('$e');
    }
  }

  void getSliderData() async {
    try {
      List<SliderModel> slider = await networkSlider();
      sliderList.value = slider;
    } catch (e) {
      networkResponseErrorHandler('$e');
    }
  }

  void refreshData() {
    loadDashboardData();
  }

  void navigateToOrders() {
    Get.toNamed('/company-orders');
  }

  void navigateToDrivers() {
    Get.toNamed('/company-drivers');
  }

  void navigateToProfile() {
    Get.toNamed('/company-profile');
  }
}
