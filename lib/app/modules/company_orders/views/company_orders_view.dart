import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/routes/app_pages.dart';
import 'package:app/app/utils/theme/app_colors.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/widgets/app_button.dart';
import 'package:app/app/widgets/default/message_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/company_orders_controller.dart';

class CompanyOrdersView extends GetView<CompanyOrdersController> {
  const CompanyOrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Strings().myOrders),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: controller.refreshCurrentTab,
            icon: Icon(Icons.refresh),
          ),
        ],
        bottom: TabBar(
          controller: controller.tabController,
          tabs: [
            Tab(text: Strings().theoffers),
            Tab(text: Strings().myOrders),
          ],
        ),
      ),
      body: TabBarView(
        controller: controller.tabController,
        children: [
          _buildMyOrdersTab(),
          _buildReceivedOrdersTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.toNamed(Routes.COMPANY_CREATE_ORDER),
        backgroundColor: AppColor.clr(AppColor.primary),
        child: Icon(Icons.add),
      ),
    );
  }

  Widget _buildMyOrdersTab() {
    return Obx(() {
      if (controller.myOrdersLoading.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.myOrdersError.value.isNotEmpty) {
        return MessageWidget(
          type: 'error',
          message: controller.myOrdersError.value,
          refresh: controller.loadMyOrders,
        );
      }

      if (controller.myOrders.isEmpty) {
        return MessageWidget(
          type: 'empty',
          message: Strings().noOrdersFound,
          description: Strings().noOrdersFoundDesc,
          refresh: controller.loadMyOrders,
        );
      }

      return RefreshIndicator(
        onRefresh: controller.loadMyOrders,
        child: ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: controller.myOrders.length,
          itemBuilder: (context, index) {
            final order = controller.myOrders[index];
            return _buildMyOrderCard(order);
          },
        ),
      );
    });
  }

  Widget _buildReceivedOrdersTab() {
    return Obx(() {
      if (controller.receivedOrdersLoading.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.receivedOrdersError.value.isNotEmpty) {
        return MessageWidget(
          type: 'error',
          message: controller.receivedOrdersError.value,
          refresh: controller.loadReceivedOrders,
        );
      }

      if (controller.receivedOrders.isEmpty) {
        return MessageWidget(
          type: 'empty',
          message: Strings().noOrdersFound,
          description: Strings().noOrdersFoundDesc,
          refresh: controller.loadReceivedOrders,
        );
      }

      return RefreshIndicator(
        onRefresh: controller.loadReceivedOrders,
        child: ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: controller.receivedOrders.length,
          itemBuilder: (context, index) {
            final order = controller.receivedOrders[index];
            return _buildReceivedOrderCard(order);
          },
        ),
      );
    });
  }

  Widget _buildMyOrderCard(OrderModel order) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${Strings().orderNumber}: ${order.orderNumber ?? order.id}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                _buildStatusChip(order.orderStatusName),
              ],
            ),
            SizedBox(height: 12),

            // Route Information
            _buildRouteInfo(order),
            SizedBox(height: 12),

            // Order Details
            _buildOrderDetails(order),
            SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                if (controller.canEditOrder(order))
                  Expanded(
                    child: AppButton(
                      onPressed: () => _showEditOrderDialog(order),
                      text: 'Edit',
                      backgroundColor: Colors.blue,
                    ),
                  ),
                if (controller.canEditOrder(order) && controller.canDeleteOrder(order)) SizedBox(width: 8),
                if (controller.canDeleteOrder(order))
                  Expanded(
                    child: AppButton(
                      onPressed: () => _showDeleteOrderDialog(order),
                      text: 'Delete',
                      backgroundColor: Colors.red,
                    ),
                  ),
                if (controller.canTrackOrder(order))
                  Expanded(
                    child: AppButton(
                      onPressed: () => _showTrackOrderDialog(order),
                      text: Strings().trackOrder,
                      backgroundColor: Colors.green,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceivedOrderCard(OrderModel order) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${Strings().orderNumber}: ${order.orderNumber ?? order.id}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                _buildStatusChip(order.orderStatusName),
              ],
            ),
            SizedBox(height: 12),

            // Route Information
            _buildRouteInfo(order),
            SizedBox(height: 12),

            // Order Details
            _buildOrderDetails(order),
            SizedBox(height: 16),

            // Accept Button
            if (controller.canAcceptOrder(order))
              Obx(() => SizedBox(
                    width: double.infinity,
                    child: AppButton(
                      onPressed: () => controller.acceptOrder(order.id!),
                      text: Strings().acceptOrder,
                      loading: controller.acceptOrderLoading.value,
                    ),
                  )),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteInfo(OrderModel order) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.green, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  order.startAddress ?? '-',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.red, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  order.endAddress ?? '-',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(OrderModel order) {
    return Column(
      children: [
        _buildDetailRow(Icons.calendar_today, Strings().startDate, controller.formatOrderDate(order.startOrder)),
        _buildDetailRow(Icons.calendar_today, Strings().endDate, controller.formatOrderDate(order.endOrder)),
        _buildDetailRow(Icons.scale, Strings().weight, order.weight != null ? '${order.weight} kg' : '-'),
        _buildDetailRow(Icons.local_shipping, Strings().truckType, order.truckTypeName),
        if (order.driverName != null) _buildDetailRow(Icons.person, Strings().driverName, order.driverName),
        if (order.price != null) _buildDetailRow(Icons.attach_money, Strings().price, '${order.price} IQD'),
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String? value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value ?? '-',
              style: TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String? status) {
    final color = Color(int.parse(
      controller.getOrderStatusColor(status).replaceFirst('#', '0xFF'),
    ));

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status ?? '-',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showEditOrderDialog(OrderModel order) {
    Get.dialog(
      AlertDialog(
        title: Text('Edit Order'),
        content: Text('Edit order functionality - To be implemented'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDeleteOrderDialog(OrderModel order) {
    Get.dialog(
      AlertDialog(
        title: Text('Delete Order'),
        content: Text('Are you sure you want to delete this order?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.deleteOrder(order.id!);
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showTrackOrderDialog(OrderModel order) {
    Get.dialog(
      AlertDialog(
        title: Text(Strings().trackOrder),
        content: Text('Track order functionality - To be implemented'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
