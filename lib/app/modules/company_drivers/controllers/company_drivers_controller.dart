import 'package:app/app/controllers/global_controller.dart';
import 'package:app/app/data/models/user_mode.dart';
import 'package:app/app/data/network/repositories/company_drivers_repository.dart';
import 'package:app/app/data/shared_pref.dart';
import 'package:app/app/utils/helper/echo.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/generated/assets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CompanyDriversController extends GetxController {
  AppPrefs prefs = Get.find();
  GlobalController globalController = Get.find();

  final pageLoading = false.obs;
  final error = ''.obs;
  final drivers = <UserModel>[].obs;
  final searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
  }

  init() {
    loadDrivers();
  }

  Future<void> loadDrivers() async {
    pageLoading.value = true;
    error.value = '';

    try {
      drivers.value = await networkCompanyDrivers();
    } catch (e) {
      kEcho("error $e");
      error.value = 'Failed to load drivers';
    }

    pageLoading.value = false;
  }

  List<UserModel> get filteredDrivers {
    if (searchQuery.value.isEmpty) {
      return drivers;
    }
    return drivers.where((driver) => driver.name.toLowerCase().contains(searchQuery.value.toLowerCase()) || driver.phone.contains(searchQuery.value)).toList();
  }

  void searchDrivers(String query) {
    searchQuery.value = query;
  }

  void refreshData() {
    loadDrivers();
  }

  void viewDriverDetails(UserModel driver) {
    Get.dialog(
      AlertDialog(
        title: Text(Strings().driverDetails),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${Strings().name}: ${driver.name}'),
            Text('${Strings().phone}: ${driver.phone}'),
            Text('${Strings().truckType}: ${driver.truck?.truckType}'),
            Text('${Strings().truckDetails}: ${driver.truck?.truckDetails}'),
            Text('${Strings().truckSize}: ${driver.truck?.truckSize}'),
            Text('${Strings().location}: ${driver.address}'),
            Row(
              children: [
                Expanded(
                    child: Column(
                  children: [
                    Text(Strings().carImage),
                    CachedNetworkImage(imageUrl: driver.carImage ?? ''),
                  ],
                )),
                Expanded(
                    child: Column(
                  children: [
                    Text(Strings().carLicense),
                    CachedNetworkImage(
                      imageUrl: driver.carLicense ?? '',
                      errorWidget: (context, url, error) {
                        return Image.asset(Assets.assetsImgLogo);
                      },
                    ),
                  ],
                )),
              ],
            )
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(Strings().close),
          ),
        ],
      ),
    );
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return Colors.green;
      case 'busy':
        return Colors.orange;
      case 'offline':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
