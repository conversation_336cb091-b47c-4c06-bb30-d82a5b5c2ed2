import 'package:app/app/data/models/user_mode.dart';
import 'package:app/app/utils/theme/app_colors.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/widgets/default/message_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/company_drivers_controller.dart';

class CompanyDriversView extends GetView<CompanyDriversController> {
  const CompanyDriversView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Strings().companyDrivers),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: controller.refreshData,
            icon: Icon(Icons.refresh),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.pageLoading.value) {
          return Center(child: CircularProgressIndicator());
        }

        if (controller.error.value.isNotEmpty) {
          return MessageWidget(
            type: 'error',
            message: controller.error.value,
            refresh: controller.refreshData,
          );
        }

        return Column(
          children: [
            // Search Bar
            Padding(
              padding: EdgeInsets.all(16),
              child: TextField(
                decoration: InputDecoration(
                  hintText: Strings().search,
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: controller.searchDrivers,
              ),
            ),

            // Drivers List
            Expanded(
              child: RefreshIndicator(
                onRefresh: controller.loadDrivers,
                child: Obx(() {
                  final filteredDrivers = controller.filteredDrivers;

                  if (filteredDrivers.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: 16),
                          Text(
                            Strings().noDriversFound,
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredDrivers.length,
                    itemBuilder: (context, index) {
                      final driver = filteredDrivers[index];
                      return _buildDriverCard(driver);
                    },
                  );
                }),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildDriverCard(UserModel driver) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Driver Header
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: AppColor.clr(AppColor.primary).withOpacity(0.1),
                  child: Icon(
                    Icons.person,
                    color: AppColor.clr(AppColor.primary),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driver.name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        driver.phone,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Driver Details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    Icons.local_shipping,
                    Strings().truckType,
                    driver.truck?.truckType ?? '-',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.location_on,
                    Strings().location,
                    driver.address ?? '-',
                  ),
                ),
              ],
            ),

        
            SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => controller.viewDriverDetails(driver),
                    icon: Icon(Icons.visibility, size: 16),
                    label: Text(Strings().viewDetails),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColor.clr(AppColor.primary),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
