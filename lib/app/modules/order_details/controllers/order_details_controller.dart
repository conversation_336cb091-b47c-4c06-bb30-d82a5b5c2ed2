import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/data/network/repositories/company_orders_repository.dart';
import 'package:app/app/data/network/repositories/driver_order_track_repository.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/utils/custom_snackbar.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

class OrderDetailsController extends GetxController {
  // Order data
  final order = Rxn<OrderModel>();
  final isLoading = false.obs;
  final error = ''.obs;

  // Order ID from deep link
  int? orderId;

  @override
  void onInit() {
    super.onInit();

    // Check if order was passed as argument (from normal navigation)
    if (Get.arguments != null && Get.arguments is OrderModel) {
      order.value = Get.arguments as OrderModel;
      orderId = order.value!.id!;
    }
    // Check if order ID was passed as parameter (from deep link)
    else if (Get.parameters['orderId'] != null) {
      orderId = int.tryParse(Get.parameters['orderId']!);
      if (orderId != null) {
        _fetchOrderDetails(orderId!);
      } else {
        error.value = 'Invalid order ID';
      }
    } else {
      error.value = 'No order data provided';
    }
    _fetchOrderDetails(orderId!);
  }

  Future<void> _fetchOrderDetails(int orderIdParam) async {
    isLoading.value = true;
    error.value = '';

    try {
      order.value = await networkGetCompanyOrderDetails(orderId: orderIdParam);
    } catch (e) {
      error.value = 'Failed to load order details: $e';
      AppSnackbar.show(
        message: 'Failed to load order details',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void shareOrder() {
    if (order.value == null) return;

    final orderId = order.value!.id ?? order.value!.orderNumber;
    if (orderId == null) {
      AppSnackbar.show(
        message: 'Cannot share order: No order ID available',
      );
      return;
    }

    final deepLink = 'hmolty://order/$orderId';
    final shareText = '''
${Strings().orderDetails}

${Strings().orderNumber}: ${order.value!.orderNumber ?? order.value!.id}
${Strings().companyNameField}: ${order.value!.companyName ?? '-'}
${Strings().startLocation}: ${order.value!.startAddress ?? '-'}
${Strings().destination}: ${order.value!.endAddress ?? '-'}
${Strings().weight}: ${order.value!.weight ?? '-'} kg
${Strings().price}: ${order.value!.price ?? '-'} IQD

${Strings().share}: $deepLink
''';

    Share.share(
      shareText,
      subject: '${Strings().orderDetails} - ${order.value!.orderNumber ?? order.value!.id}',
    );
  }

  void refreshOrder() {
    if (orderId != null) {
      _fetchOrderDetails(orderId!);
    }
  }

  // Getters for UI
  String get orderNumberText {
    return '${Strings().orderNumber}: ${order.value?.orderNumber ?? order.value?.id ?? '-'}';
  }

  String get orderStatusText {
    return order.value?.orderStatusName ?? Strings().tripStart;
  }

  String get startAddressText {
    return order.value?.startAddress ?? '-';
  }

  String get endAddressText {
    return order.value?.endAddress ?? '-';
  }

  String get companyNameText {
    return order.value?.companyName ?? '-';
  }

  String get driverNameText {
    return order.value?.driverName ?? '-';
  }

  String get weightText {
    return order.value?.weight != null ? '${order.value!.weight} kg' : '-';
  }

  String get priceText {
    return order.value?.price != null ? '${order.value!.price} IQD' : '-';
  }

  String get truckTypeText {
    return order.value?.truckTypeName ?? '-';
  }

  String get descriptionText {
    return order.value?.description ?? '-';
  }

  String get notesText {
    return order.value?.notes ?? '-';
  }
}
