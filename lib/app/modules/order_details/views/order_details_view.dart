import 'package:app/app/utils/theme/app_colors.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:get/get.dart';
import '../controllers/order_details_controller.dart';

class OrderDetailsView extends GetView<OrderDetailsController> {
  const OrderDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Strings().orderDetails),
        centerTitle: true,
        backgroundColor: AppColor.clr(AppColor.primary),
        foregroundColor: Colors.white,
        actions: [
          Obx(() => controller.order.value != null
              ? IconButton(
                  onPressed: controller.shareOrder,
                  icon: Icon(Icons.share),
                  tooltip: Strings().share,
                )
              : SizedBox.shrink()),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColor.clr(AppColor.primary),
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  Strings().loading,
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          );
        }

        if (controller.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                SizedBox(height: 16),
                Text(
                  controller.error.value,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refreshOrder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColor.clr(AppColor.primary),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(Strings().retry),
                ),
              ],
            ),
          );
        }

        if (controller.order.value == null) {
          return Center(
            child: Text(
              Strings().orderDetailsNotAvailable,
              style: TextStyle(fontSize: 16),
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order Header Card
              _buildOrderHeaderCard(),
              SizedBox(height: 16),

              // Current Location Map (if available)
              if (controller.hasTrackLocation) ...[
                _buildTrackingMapCard(),
                SizedBox(height: 16),
              ],

              // Location Details Card (Expandable)
              _buildLocationCard(),
              SizedBox(height: 16),

              // Order Details Card (Expandable)
              _buildOrderDetailsCard(),
              SizedBox(height: 16),

              // Order Tracking Timeline (Expandable)
              if (controller.order.value?.orderTrack != null &&
                  controller.order.value!.orderTrack!.isNotEmpty) ...[
                _buildOrderTrackingCard(),
                SizedBox(height: 16),
              ],

              // Additional Information Card (Expandable)
              _buildAdditionalInfoCard(),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildOrderHeaderCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.local_shipping,
                  color: AppColor.clr(AppColor.primary),
                  size: 24,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        controller.orderNumberText,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        controller.orderStatusText,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColor.clr(AppColor.primary),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: controller.shareOrder,
                  icon: Icon(
                    Icons.share,
                    color: AppColor.clr(AppColor.primary),
                  ),
                  tooltip: Strings().share,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard() {
    return Obx(() => Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Theme(
        data: Theme.of(Get.context!).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: controller.isLocationExpanded.value,
          onExpansionChanged: (expanded) => controller.isLocationExpanded.value = expanded,
          leading: Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColor.clr(AppColor.primary).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.route,
              color: AppColor.clr(AppColor.primary),
              size: 20,
            ),
          ),
          title: Text(
            Strings().routeInformation,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  // Start location
                  _buildLocationRow(
                    icon: Icons.location_on,
                    iconColor: Colors.green,
                    label: Strings().startLocation,
                    address: controller.startAddressText,
                  ),
                  SizedBox(height: 16),

                  // Destination
                  _buildLocationRow(
                    icon: Icons.location_on,
                    iconColor: Colors.red,
                    label: Strings().destination,
                    address: controller.endAddressText,
                  ),

                  // Current tracking location (if available)
                  if (controller.hasTrackLocation) ...[
                    SizedBox(height: 16),
                    _buildLocationRow(
                      icon: Icons.my_location,
                      iconColor: AppColor.clr(AppColor.primary),
                      label: 'Current Location',
                      address: controller.trackAddressText,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildLocationRow({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String address,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: iconColor, size: 20),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 4),
              Text(
                address,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderDetailsCard() {
    return Obx(() => Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Theme(
        data: Theme.of(Get.context!).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: controller.isOrderDetailsExpanded.value,
          onExpansionChanged: (expanded) => controller.isOrderDetailsExpanded.value = expanded,
          leading: Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColor.clr(AppColor.primary).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.info_outline,
              color: AppColor.clr(AppColor.primary),
              size: 20,
            ),
          ),
          title: Text(
            Strings().orderDetails,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  _buildDetailRow(Icons.business, Strings().companyNameField, controller.companyNameText),
                  _buildDetailRow(Icons.person, Strings().driverName, controller.driverNameText),
                  _buildDetailRow(Icons.scale, Strings().weight, controller.weightText),
                  _buildDetailRow(Icons.local_shipping, Strings().truckType, controller.truckTypeText),
                  _buildDetailRow(Icons.straighten, 'Truck Size', controller.truckSizeText),
                  _buildDetailRow(Icons.attach_money, Strings().price, controller.priceText),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildAdditionalInfoCard() {
    return Obx(() => Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Theme(
        data: Theme.of(Get.context!).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: controller.isAdditionalInfoExpanded.value,
          onExpansionChanged: (expanded) => controller.isAdditionalInfoExpanded.value = expanded,
          leading: Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColor.clr(AppColor.primary).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.description,
              color: AppColor.clr(AppColor.primary),
              size: 20,
            ),
          ),
          title: Text(
            Strings().additionalInformation,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  _buildDetailRow(Icons.description, Strings().description, controller.descriptionText),
                  _buildDetailRow(Icons.note, Strings().notes, controller.notesText),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildTrackingMapCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColor.clr(AppColor.primary).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.my_location,
                    color: AppColor.clr(AppColor.primary),
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Location',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        'Live tracking location',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 200,
            margin: EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: FlutterMap(
                options: MapOptions(
                  initialCenter: LatLng(
                    controller.trackLatitude ?? 33.3152,
                    controller.trackLongitude ?? 44.3661,
                  ),
                  initialZoom: 15.0,
                  interactionOptions: InteractionOptions(
                    flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
                  ),
                ),
                children: [
                  TileLayer(
                    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.hmolty.app',
                  ),
                  if (controller.hasTrackLocation)
                    MarkerLayer(
                      markers: [
                        Marker(
                          point: LatLng(
                            controller.trackLatitude!,
                            controller.trackLongitude!,
                          ),
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: AppColor.clr(AppColor.primary),
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: Icon(
                              Icons.local_shipping,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderTrackingCard() {
    return Obx(() => Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Theme(
        data: Theme.of(Get.context!).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: controller.isTrackingExpanded.value,
          onExpansionChanged: (expanded) => controller.isTrackingExpanded.value = expanded,
          leading: Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColor.clr(AppColor.primary).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.timeline,
              color: AppColor.clr(AppColor.primary),
              size: 20,
            ),
          ),
          title: Text(
            'Order Tracking',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: controller.order.value!.orderTrack!.asMap().entries.map((entry) {
                  int index = entry.key;
                  var track = entry.value;
                  bool isLast = index == controller.order.value!.orderTrack!.length - 1;

                  return _buildTrackingItem(
                    status: track.orderStatus ?? '',
                    time: track.createdAt ?? '',
                    isLast: isLast,
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildTrackingItem({
    required String status,
    required String time,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: AppColor.clr(AppColor.primary),
                shape: BoxShape.circle,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: Colors.grey.shade300,
              ),
          ],
        ),
        SizedBox(width: 12),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }
}
