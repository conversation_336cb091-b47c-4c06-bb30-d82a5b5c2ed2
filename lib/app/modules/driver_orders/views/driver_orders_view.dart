import 'package:app/app/data/models/general_data_models.dart';
import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/routes/app_pages.dart';
import 'package:app/app/utils/helper/permission_helper.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/widgets/app_bottomsheet.dart';
import 'package:app/app/widgets/app_button.dart';
import 'package:app/app/utils/custom_snackbar.dart';
import 'package:app/app/widgets/default/edit_text.dart';
import 'package:app/app/widgets/default/message_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/driver_orders_controller.dart';

class DriverOrdersView extends GetView<DriverOrdersController> {
  const DriverOrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Strings().orders),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: controller.refreshCurrentTab,
            icon: Icon(Icons.refresh),
          ),
        ],
        bottom: TabBar(
          controller: controller.tabController,
          tabs: [
            Tab(text: Strings().theoffers),
            Tab(text: Strings().myOrders),
          ],
        ),
      ),
      body: TabBarView(
        controller: controller.tabController,
        children: [
          _buildAvailableOrdersTab(),
          _buildMyOrdersTab(),
        ],
      ),
    );
  }

  Widget _buildAvailableOrdersTab() {
    return Obx(() {
      if (controller.availableOrdersLoading.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.availableOrdersError.value.isNotEmpty) {
        return MessageWidget(
          type: 'error',
          message: controller.availableOrdersError.value,
          refresh: controller.loadAvailableOrders,
        );
      }

      if (controller.availableOrders.isEmpty) {
        return MessageWidget(
          type: 'empty',
          message: Strings().noOrdersFound,
          description: Strings().noOrdersFoundDesc,
          refresh: controller.loadAvailableOrders,
        );
      }

      return RefreshIndicator(
        onRefresh: controller.loadAvailableOrders,
        child: ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: controller.availableOrders.length,
          itemBuilder: (context, index) {
            final order = controller.availableOrders[index];
            return _buildAvailableOrderCard(order);
          },
        ),
      );
    });
  }

  Widget _buildMyOrdersTab() {
    return Obx(() {
      if (controller.myOrdersLoading.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.myOrdersError.value.isNotEmpty) {
        return MessageWidget(
          type: 'error',
          message: controller.myOrdersError.value,
          refresh: controller.loadMyOrders,
        );
      }

      if (controller.myOrders.isEmpty) {
        return MessageWidget(
          type: 'empty',
          message: Strings().noOrdersFound,
          description: Strings().noOrdersFoundDesc,
          refresh: controller.loadMyOrders,
        );
      }

      return RefreshIndicator(
        onRefresh: controller.loadMyOrders,
        child: ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: controller.myOrders.length,
          itemBuilder: (context, index) {
            final order = controller.myOrders[index];
            return _buildMyOrderCard(order);
          },
        ),
      );
    });
  }

  Widget _buildAvailableOrderCard(OrderModel order) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${Strings().orderNumber}: ${order.orderNumber ?? order.id}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                _buildStatusChip(order.orderStatusName),
              ],
            ),
            SizedBox(height: 12),

            // Route Information
            _buildRouteInfo(order),
            SizedBox(height: 12),

            // Order Details
            _buildOrderDetails(order),
            SizedBox(height: 16),

            // Accept Button
            if (controller.canAcceptOrder(order))
              Obx(() => SizedBox(
                    width: double.infinity,
                    child: AppButton(
                      onPressed: () => controller.acceptOrder(order.id!),
                      text: Strings().acceptOrder,
                      loading: controller.acceptOrderLoading.value,
                    ),
                  )),
          ],
        ),
      ),
    );
  }

  Widget _buildMyOrderCard(OrderModel order) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${Strings().orderNumber}: ${order.orderNumber ?? order.id}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                _buildStatusChip(order.orderStatusName),
              ],
            ),
            SizedBox(height: 12),

            // Route Information
            _buildRouteInfo(order),
            SizedBox(height: 12),

            // Order Details
            _buildOrderDetails(order),
            SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                // Trip Start Button - for orders with "بداية الرحلة" status
                if (_canStartTrip(order))
                  Expanded(
                    child: AppButton(
                      onPressed: () => _navigateToTrip(order),
                      text: Strings().startTrip,
                      backgroundColor: Colors.green,
                    ),
                  ),
                if (_canStartTrip(order) && (controller.canTrackOrder(order) || controller.canChangeStatus(order))) SizedBox(width: 8),

                if (controller.canTrackOrder(order))
                  Expanded(
                    child: AppButton(
                      onPressed: () => _showTrackOrderDialog(order),
                      text: Strings().trackOrder,
                      backgroundColor: Colors.blue,
                    ),
                  ),
                if (controller.canTrackOrder(order) && controller.canChangeStatus(order)) SizedBox(width: 8),
                if (controller.canChangeStatus(order))
                  Expanded(
                    child: AppButton(
                      onPressed: () => _showChangeStatusDialog(order),
                      text: Strings().orderStatus,
                      backgroundColor: Colors.orange,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteInfo(OrderModel order) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.green, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  order.startAddress ?? '-',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.red, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  order.endAddress ?? '-',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(OrderModel order) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildDetailRow(Icons.calendar_today, Strings().startDate, controller.formatOrderDate(order.startOrder))),
            Expanded(child: _buildDetailRow(Icons.timelapse, Strings().startTime, controller.formatOrderTime(order.startOrder))),
          ],
        ),
        Row(
          children: [
            Expanded(child: _buildDetailRow(Icons.calendar_today, Strings().endDate, controller.formatOrderDate(order.endOrder))),
            Expanded(child: _buildDetailRow(Icons.timelapse, Strings().endTime, controller.formatOrderTime(order.endOrder))),
          ],
        ),
        _buildDetailRow(Icons.scale, Strings().weight, order.weight != null ? '${order.weight} kg' : '-'),
        _buildDetailRow(Icons.local_shipping, Strings().truckType, order.truckTypeName),
        if (order.companyName != null) _buildDetailRow(Icons.business, Strings().companyNameField, order.companyName),
        if (order.price != null) _buildDetailRow(Icons.attach_money, Strings().price, '${order.price} IQD'),
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String? value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text('$label: ', style: TextStyle(fontSize: 14, color: Colors.black87)),
          Expanded(child: Text(value ?? '-', style: TextStyle(fontSize: 14, color: Colors.black87))),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String? status) {
    final color = Color(int.parse(
      controller.getOrderStatusColor(status).replaceFirst('#', '0xFF'),
    ));

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status ?? '-',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showTrackOrderDialog(OrderModel order) {
    // Implementation for tracking order dialog
    Get.dialog(
      AlertDialog(
        title: Text(Strings().trackOrder),
        content: Text('Track order functionality - To be implemented'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showChangeStatusDialog(OrderModel order) {
    AppBottomSheets().show(
      context: Get.context!,
      height: Get.height * 0.8,
      onFinishedCallback: (result) {},
      child: _buildChangeStatusBottomSheet(order),
    );
  }

  Widget _buildChangeStatusBottomSheet(OrderModel order) {
    final selectedStatus = Rx<OrderStatusModel?>(null);
    final notesController = TextEditingController();
    final isLoading = false.obs;

    return Container(
      padding: EdgeInsets.all(20),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  Strings().changeOrderStatus,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 20),

            // Current Status
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    '${Strings().orderStatus}: ${order.orderStatusName ?? '-'}',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),

            // Status Selection
            Text(
              Strings().selectOrderStatus,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 12),

            FutureBuilder<List<OrderStatusModel>>(
              future: controller.getOrderStatuses(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                  return Container(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Failed to load order statuses',
                      style: TextStyle(color: Colors.red),
                    ),
                  );
                }

                final statuses = snapshot.data!;
                return Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: statuses.map((status) {
                      return Obx(() => RadioListTile<OrderStatusModel>(
                            title: Text(status.name),
                            value: status,
                            groupValue: selectedStatus.value,
                            onChanged: (value) {
                              selectedStatus.value = value;
                            },
                          ));
                    }).toList(),
                  ),
                );
              },
            ),
            SizedBox(height: 20),

            // Notes Section
            Obx(() {
              final showNotes = selectedStatus.value?.id == 8 || selectedStatus.value != null;
              return AnimatedContainer(
                duration: Duration(milliseconds: 300),
                height: showNotes ? null : 0,
                child: showNotes
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                Strings().notes,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (selectedStatus.value?.id == 8)
                                Text(
                                  ' *',
                                  style: TextStyle(color: Colors.red),
                                ),
                            ],
                          ),
                          SizedBox(height: 8),
                          EditText(
                            hint: selectedStatus.value?.id == 8 ? Strings().notesRequired : Strings().enterNotesForStatus,
                            value: '',
                            control: notesController,
                            lines: 3,
                            type: TextInputType.multiline,
                          ),
                          SizedBox(height: 20),
                        ],
                      )
                    : SizedBox.shrink(),
              );
            }),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: AppButton(
                    onPressed: () => Get.back(),
                    text: Strings().cancel,
                    backgroundColor: Colors.grey,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Obx(() => AppButton(
                        onPressed: () {
                          if (selectedStatus.value != null) {
                            _handleStatusChange(
                              order,
                              selectedStatus.value!,
                              notesController.text,
                              isLoading,
                            );
                          }
                        },
                        text: Strings().updateStatus,
                        loading: isLoading.value,
                        isDisabled: selectedStatus.value == null,
                      )),
                ),
              ],
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _handleStatusChange(
    OrderModel order,
    OrderStatusModel selectedStatus,
    String notes,
    RxBool isLoading,
  ) async {
    // Validate notes if required for status ID 8
    if (selectedStatus.id == 8 && (notes.isEmpty || notes.trim().isEmpty)) {
      Get.snackbar(
        'Error',
        Strings().notesRequired,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;

    try {
      await controller.changeOrderStatus(
        orderId: order.id!,
        orderStatusId: selectedStatus.id,
        notes: notes.isNotEmpty ? notes : null,
      );
      Get.back(); // Close the bottom sheet
    } catch (e) {
      // Error handling is done in the controller
    } finally {
      isLoading.value = false;
    }
  }

  // Helper method to check if order can start trip
  bool _canStartTrip(OrderModel order) {
    // Check if order status is "بداية الرحلة" (Trip Start)
    return (order.orderStatusName?.contains('بداية الرحلة')  ?? false)||
           order.orderStatusName?.toLowerCase() == 'trip start';
  }

  // Navigate to trip screen with location permission check
  Future<void> _navigateToTrip(OrderModel order) async {
    try {
      // Check location permission first
      bool hasPermission = await PermissionHelper.location();

      if (hasPermission) {
        // Navigate to DriverTrip screen with order data
        Get.toNamed(Routes.DRIVER_TRIP, arguments: order);
      } else {
        // Show permission denied message
        AppSnackbar.show(message: Strings().locationPermissionDenied);
      }
    } catch (e) {
      // Handle permission error
      AppSnackbar.show(message: Strings().locationPermissionDenied);
    }
  }
}
