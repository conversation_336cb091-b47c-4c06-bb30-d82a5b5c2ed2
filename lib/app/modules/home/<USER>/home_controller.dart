import 'package:app/app/data/shared_pref.dart';
import 'package:app/app/utils/helper/echo.dart';
import 'package:get/get.dart';

class HomeController extends GetxController {
  final pageLoading = false.obs;
  final error = ''.obs;
  AppPrefs prefs = Get.find();
  @override
  void onInit() {
    super.onInit();
  }

  Future<void> getData() async {
    error.value = '';
    pageLoading.value = true;
    try {
    } catch (e) {
      kEchoError(e.toString());
      error.value = e.toString();
    }
    pageLoading.value = false;
  }
}
