import 'package:app/app/data/models/slider_model.dart';
import 'package:app/app/utils/theme/app_colors.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class HomeSlider extends StatefulWidget {
  final List<SliderModel> sliderModel;
  const HomeSlider({
    super.key,
    required this.sliderModel,
  });

  @override
  State<HomeSlider> createState() => _HomeSliderState();
}


class _HomeSliderState extends State<HomeSlider> {
  int currentSliderPosition = 0;
  @override
  Widget build(BuildContext context) {
    if (widget.sliderModel.isEmpty) return Container();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        children: <Widget>[
          CarouselSlider(
            items: widget.sliderModel.map((singleSlider) {
              return GestureDetector(
                onTap: () async {
                  bool canLan = await canLaunch(singleSlider.link ?? '');
                  await launch(singleSlider.link!);
                },
                child: Container(
                  margin: EdgeInsets.only(bottom: 2),
                  width: MediaQuery.of(context).size.width,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: CachedNetworkImage(
                      imageUrl: singleSlider.image ?? '',
                      errorWidget: (vtx, url, obj) {
                        return Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        );
                      },
                      placeholder: (ctx, url) {
                        return Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        );
                      },
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
              );
            }).toList(),
            options: CarouselOptions(
              aspectRatio: 2.3,
              viewportFraction: 1.0,
              autoPlayInterval: Duration(seconds: 4),
              autoPlayAnimationDuration: Duration(milliseconds: 650),
              enlargeCenterPage: false,
              disableCenter: false,
              scrollDirection: Axis.horizontal,
              initialPage: currentSliderPosition,
              enableInfiniteScroll: true,
              reverse: false,
              autoPlay: widget.sliderModel.isNotEmpty ? true : false,
              onPageChanged: (pageNo, reason) {
                currentSliderPosition = pageNo;
                setState(() {});
              },
            ),
          ),
          SizedBox(height: 4),
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                ...widget.sliderModel.map((singleString) {
                  var index = widget.sliderModel.indexOf(singleString);
                  return Container(
                    width: widget.sliderModel.length > 10 ? 6 : 24.0,
                    height: 4.0,
                    margin: EdgeInsets.symmetric(horizontal: widget.sliderModel.length > 10 ? 2 : 4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      borderRadius: BorderRadius.circular(80),
                      color: currentSliderPosition == index ? AppColor.clr(AppColor.secondary) : AppColor.clr(AppColor.secondary).withOpacity(0.2),
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
