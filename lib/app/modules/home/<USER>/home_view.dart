import 'package:app/app/utils/extenstions/decoration.dart';
import 'package:app/app/utils/firebase_methods.dart';
import 'package:app/app/utils/helper/const_strings.dart';
import 'package:app/app/utils/theme/app_colors.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/widgets/default/message_widget.dart';
import 'package:app/generated/assets.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:get/get.dart';
import 'package:zextensions/zextensions.dart';

import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Obx(
          () {
            if (controller.pageLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            }
            if (controller.error.isNotEmpty) {
              return MessageWidget(type: 'error', message: controller.error.value, refresh: controller.getData);
            }
            return SingleChildScrollView(
              child: Column(
                children: [
                  if (kDebugMode) ...[
                    TextButton(onPressed: () => FirebaseMethods().setInReviewStatus(true), child: const Text('Set In Review true')),
                    TextButton(onPressed: () => FirebaseMethods().setInReviewStatus(false), child: const Text('Set In Review false')),
                  ],
                  SizedBox(height: 8),
                
                  //* Products & Gifts
                  Row(
                    children: [
                      SizedBox(width: 4),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration().radius(radius: 8).customColor(AppColor.clr(AppColor.surface)).shadow(),
                            height: 150,
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.asset(Assets.assetsImgProducts, fit: BoxFit.fill, height: 150, width: double.infinity),
                                ),
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  right: 0,
                                  child: Container(
                                    padding: EdgeInsets.all(4),
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.only(bottomLeft: Radius.circular(8), bottomRight: Radius.circular(8)),
                                    ).customColor(AppColor.clr(AppColor.black).withOpacity(0.5)),
                                    child: Center(
                                      child: Text(
                                        Strings().products,
                                        style: TextStyle(fontSize: 18),
                                      ).white(),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (controller.prefs.isAuthenticated())
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(horizontal: 8),
                              decoration: BoxDecoration().radius(radius: 8).customColor(AppColor.clr(AppColor.primaryColorLight)).shadow(),
                              height: 150,
                              child: Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.asset(Assets.assetsImgGifts, fit: BoxFit.fill, height: 150, width: double.infinity),
                                  ),
                                  Positioned(
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    child: Container(
                                      padding: EdgeInsets.all(4),
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(8), bottomRight: Radius.circular(8)),
                                      ).customColor(AppColor.clr(AppColor.black).withOpacity(0.5)),
                                      child: Center(
                                        child: Text(
                                          Strings().gifts,
                                          style: TextStyle(fontSize: 18),
                                        ).white(),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                                .animate(
                                  delay: 2000.ms, // this delay only happens once at the very start
                                  onPlay: (controller) => controller.repeat(), // loop
                                )
                                .shake(delay: 800.ms, duration: 400.ms, hz: 4, rotation: 0.02),
                          ),
                        ),
                      SizedBox(width: 4),
                    ],
                  ).animate(delay: 300.ms).fadeIn(duration: 300.ms),
                  //* Latest Products

                  SizedBox(height: 20),
                  Text('version ${StringConst.APP_VERSION} @ حمولتي'),
                  SizedBox(height: 4),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
