import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/modules/driver_orders/controllers/driver_orders_controller.dart';
import 'package:app/app/modules/home/<USER>/home_slider.dart';
import 'package:app/app/modules/home/<USER>/home_view.dart';
import 'package:app/app/modules/layout/controllers/layout_controller.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/widgets/app_button.dart';
import 'package:app/app/widgets/default/message_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zextensions/zextensions.dart';
import '../controllers/driver_home_controller.dart';

class DriverHomeView extends GetView<DriverHomeController> {
  const DriverHomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Strings().appName),
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: () {},
            icon: Icon(Icons.notifications),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.pageLoading.value) {
          return Center(child: CircularProgressIndicator());
        }

        if (controller.error.value.isNotEmpty) {
          return MessageWidget(
            type: 'error',
            message: controller.error.value,
            refresh: controller.refreshData,
          );
        }

        return RefreshIndicator(
          onRefresh: controller.getNetworkData,
          child: SingleChildScrollView(
            physics: AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 8),
                if (controller.sliderList.isNotEmpty) HomeSlider(sliderModel: controller.sliderList),
                SizedBox(height: 8),
                if (controller.orders.isNotEmpty) _buildOrderOffers(),
                SizedBox(height: 24),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildStatisticsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings().ordersStatistics,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                Strings().totalOrders,
                controller.totalOrders.value.toString(),
                Icons.assignment,
                Colors.blue,
                controller.navigateToOrders,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                Strings().activeOrders,
                controller.activeOrders.value.toString(),
                Icons.local_shipping,
                Colors.orange,
                controller.navigateToOrders,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                Strings().completeOrders,
                controller.completedOrders.value.toString(),
                Icons.check_circle,
                Colors.green,
                controller.navigateToOrders,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 28,
                color: color,
              ),
              SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'My Orders',
                Icons.assignment,
                Colors.blue,
                () => controller.navigateToOrders(),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'Balance',
                Icons.account_balance_wallet,
                Colors.green,
                () => controller.navigateToBalance(),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'Profile',
                Icons.person,
                Colors.orange,
                () => controller.navigateToProfile(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 28,
                color: color,
              ),
              SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentOrdersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings().recentOrders,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16),
        ...controller.orders.map((e) => _buildOrderItem(e)).toList(),
      ],
    );
  }

  Widget _buildOrderItem(OrderModel order) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: buildOrderStatusColor(order.orderStatusName),
            child: Icon(
              order.orderStatusName == 'Completed' ? Icons.check_circle : Icons.local_shipping,
              color: Colors.white,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${Strings().orderNumber} ${order.orderNumber}',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  order.endAddress ?? '',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: buildOrderStatusColor(order.orderStatusName).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  order.orderStatusName ?? '',
                  style: TextStyle(
                    color: buildOrderStatusColor(order.orderStatusName),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  buildOrderStatusColor(String? orderStatusName) {
    if (orderStatusName == 'قيد التنفيذ') return Colors.grey;
    if (orderStatusName == 'قبول السائق') return Colors.blue;
    if (orderStatusName == 'الوصول  إلى المخزن') return Colors.green;
    if (orderStatusName == 'تحميل الحمولة') return Colors.green;
    if (orderStatusName == 'بداية الرحلة') return Colors.green;
    if (orderStatusName == 'تاخير الوصول') return Colors.green;
    if (orderStatusName == 'وصول البضاعة') return Colors.green;
    if (orderStatusName == 'عدم التسليم بسبب') return Colors.green;
    if (orderStatusName == 'استلام الحمولة') return Colors.green;
    if (orderStatusName == 'تسليم من قبل السائق') return Colors.green;
    if (orderStatusName == 'تسليم من قبل الشركة') return Colors.green;
    return Colors.grey;
  }

  _buildOrderOffers() {
    return Column(
      children: [
        Row(
          children: [
            Text(Strings().theoffers),
            Spacer(),
            GestureDetector(
              onTap: (){
                LayoutController layoutController = Get.find();
                layoutController.selectedBottomNavIndex.value = 1;
                  DriverOrdersController driverOrdersController = Get.find();
                  driverOrdersController.init(); 
              },
              child: Container(
                color: Colors.transparent,
                padding: const EdgeInsets.all(8.0),
                child: Text(Strings().showAll).fontSize(12),
              )),
          ],
        ),
        SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...controller.orders.map(
                (element) {
                  return Container(
                    width: Get.width / 2.3,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), border: Border.all(color: Colors.grey)),
                    padding: EdgeInsets.all(8),
                    child: Column(
                      children: [
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: '${Strings().tripDestination}: \n',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: '${Strings().from}: ',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: ' ${element.startAddress}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: ' ${Strings().to}: ',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: '${element.endAddress}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: '${Strings().weight}: ',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: ' ${element.weight}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: '${Strings().truckType}: ',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: ' ${element.truckTypeName}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: '${Strings().price}: ',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: ' ${element.price ?? "-"}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 12),
                        SizedBox(
                          width: double.infinity,
                          child: AppButton(
                            onPressed: () {
                              Get.toNamed('/order-details', arguments: element);
                            },
                            text: Strings().showDetails,
                          ),
                        )
                      ],
                    ),
                  );
                },
              )
            ],
          ),
        ),
      ],
    );
  }
}
