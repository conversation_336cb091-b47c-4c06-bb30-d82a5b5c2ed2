import 'package:app/app/controllers/global_controller.dart';
import 'package:app/app/data/models/driver_home_response.dart';
import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/data/models/slider_model.dart';
import 'package:app/app/data/network/repositories/driver_home_repository.dart';
import 'package:app/app/data/network/repositories/driver_orders_repository.dart';
import 'package:app/app/data/network/repositories/slider_repository.dart';
import 'package:app/app/data/shared_pref.dart';
import 'package:app/app/utils/helper/network_error_response_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class DriverHomeController extends GetxController {
  AppPrefs prefs = Get.find();
  GlobalController globalController = Get.find();

  final pageLoading = false.obs;
  final error = ''.obs;

  final totalOrders = 0.obs;
  final activeOrders = 0.obs;
  final completedOrders = 0.obs;

  final sliderList = RxList<SliderModel>();

  final orders = <OrderModel>[].obs;

  @override
  void onInit() {
    super.onInit();
  }

  init({bool ignoreIfSet = false}) {
    if (ignoreIfSet && sliderList.isNotEmpty && orders.isNotEmpty) return;
    getNetworkData();
    getSliderData();
    getRecentOrders();
  }

  void refreshData() {
    getSliderData();
    getRecentOrders();
  }

  void navigateToOrders() {
    Get.toNamed('/driver-orders');
  }

  void navigateToBalance() {
    Get.toNamed('/driver-balance');
  }

  void navigateToProfile() {
    Get.toNamed('/driver-profile');
  }

  String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} IQD';
  }

  Future<void> getNetworkData() async {
    error.value = '';
    pageLoading.value = true;

    try {
      DriverHomeResponse driverHomeResponse = await networkGetDriverHome();

      totalOrders.value = driverHomeResponse.notificationCount;
      activeOrders.value = driverHomeResponse.currentActiveOrders;
      completedOrders.value = driverHomeResponse.completedOrders;
    } catch (e) {
      networkResponseErrorHandler('$e');
      if (kDebugMode) {
        error.value = e.toString();
      } else {
        error.value = 'Failed to load balance';
      }
    }

    pageLoading.value = false;
  }

  Future<void> getRecentOrders() async {
    try {
      List<OrderModel> orders = await networkGetDriverAvailableOffers();
      this.orders.value = orders;
    } catch (e) {
      networkResponseErrorHandler('$e');
    }
  }

  void getSliderData() async {
    try {
      List<SliderModel> slider = await networkSlider();
      sliderList.value = slider;
    } catch (e) {
      networkResponseErrorHandler('$e');
    }
  }

//  void toggleDriverStatus() {
//     if (driverStatus.value == 'Available') {
//       driverStatus.value = 'Busy';
//     } else {
//       driverStatus.value = 'Available';
//     }
//     // TODO: Update status on server
//   }
}
