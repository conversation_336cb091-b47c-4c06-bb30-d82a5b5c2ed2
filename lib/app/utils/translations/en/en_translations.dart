final Map<String, String> enTranslation = {
  "viewDetails": 'view details',
  "noDriversFound": 'No drivers found',
  "rating": 'Rating',
  "location": 'Location',
  "status": 'Status',
  "companyDrivers": 'Company drivers',
  "showAll": 'Show all',
  "theoffers": 'The offers',
  "amountIQD": 'Amount (IQD)',
  "minimumRechargeAmount": 'Minimum Recharge Amount: 1000 IQD',
  "pleaseEnterAmount": 'Please enter amount',
  "pleaseEnterValidAmount": 'Please enter valid amount',
  "recharge": 'Recharge',
  "account": 'Account',
  "supportAndSharing": 'Support & Sharing',
  "aboutAndLegal": 'About & Legal',
  "orders": 'Orders',
  "ordersStatistics": 'Orders statistics',
  "totalOrders": 'Total orders',
  "activeOrders": 'Active orders',
  "completeOrders": 'Complete orders',
  "endTime": 'End time',
  "startTime": 'Start time',
  "city": 'City',
  "edit": 'Edit',
  'address': 'Address',
  'orderNow': 'Order Now',
  'areYouSureYouWantToRequestThisGift': 'Are you sure you want to request this gift',
  'requestGift': 'Request Gift',
  'loginAsGuest': 'Login as Guest',
  'dismiss': 'Dismiss',
  'products': 'products',
  'gifts': 'gifts',
  'myGifts': 'My Gifts',
  'welcomeMessage': 'Welcome back, please login to continue',
  'changeLanguage': 'Change Language',
  'highlight': 'Highlight',
  'highlights': 'Highlights',
  'notifications': 'Notifications',
  'editData': 'Edit Data',
  'dontHaveAnAccount': 'Don\'t have an account?',
  'and': 'And',
  'paste': 'Paste',
  'iAgreeTo': 'I agree to',
  'termsAndConditions': 'Terms and Conditions',
  'privacyPolicy': 'Privacy Policy',
  'privacyPolicyAndTerms': 'Privacy Policy and Terms',
  'cut': 'Cut',
  'update': 'Update',
  'linesHeight': 'Lines Height',
  'all': 'All',
  'pleaseWaitUntrilSearchSyncFinished': 'Please wait until search sync is finished',
  'checkDisplaySetting': 'Check Display Settings',
  'settingDisplay': 'Display Setting',
  'myFiles': 'My Files',
  'objectiveIndex': 'Objective Index',
  'to': 'To',
  'from': 'From',
  'pages': 'Pages',
  'delete': 'Delete',
  'selectAll': 'Select All',
  'writeSeachWordHere': 'Write search word here',
  'pressAgainToExit': 'Press again to exit',
  'couldntOpenUrl': 'Couldn\'t open URL',
  'rateApp': 'Rate App',
  'shareApp': 'Share App',
  'shareAppMessage': 'Share App Message',
  'forgetPasswordSuccessMessage': 'Reset password message sent successfully',
  'noResultFound': 'No results found',
  'writeAtLeast3Letters': 'Write at least 3 letters',
  'writeAtLeast10Letters': 'Write at least 10 letters',
  'theAuthor': 'The Author',
  'receiveNotifications': 'Receive Notifications',
  'nightMode': 'Night Mode',
  'fontColor': 'Font Color',
  'fontType': 'Font Type',
  'fontSize': 'Font Size',
  'refresh': 'Refresh',
  'selectWidget': 'Select Widget',
  'selectView': 'Select View',
  'selectComponent': 'Select Component',
  'copy': 'Copy',
  'share': 'Share',
  'message': 'Message',
  'emailOptional': 'Email (optional)',
  'nameOptional': 'Name (optional)',
  'contactUsNote': 'Contact Us Note',
  'cancel': 'Cancel',
  'confirm': 'Confirm',
  'logoutMessage': 'Confirm logout',
  'deleteMessage': 'Confirm delete',
  'logout': 'Logout',
  'deleteAccount': 'Delete Account',
  'agree': 'Agree',
  'send': 'Send',
  'successSendResetPasswordMessage': 'Reset password message sent successfully',
  'resetPasswordMessage': 'Reset password message',
  'resetPassword': 'Reset Password',
  'name': 'Name',
  'register': 'Register',
  'createAccount': 'Create Account',
  'registerNew': 'Register New',
  'nameValidation': 'Name Validation',
  'passwordConfirmvalidation': 'Password Confirm Validation',
  'passwordConfirm': 'Password Confirm',
  'changePassword': 'Change Password',
  'editPassword': 'Edit Password',
  'forgetPassword2': 'Forgot Password',
  'password': 'Password',
  'currentPssword': 'Current Password',
  'newPassword': 'New Password',
  'configmNewPassword': 'Confirm New Password',
  'passwordValidation': 'Password Validation',
  'close': 'Close',
  'email': 'Email',
  'newAccount': 'New Account',
  'emailValidation': 'Email Validation',
  'empty': 'Empty',
  'searchForTitleOrAuthor': 'Search for title or author',
  'notification': 'Notification',
  'noInternetConnection': 'No Internet Connection',
  'serverError': 'Server Error',
  'requireLoginMessage': 'You need to log in to proceed',
  'login': 'Login',
  'emptySearchMessage': 'No results found',
  'emptySearchMessageDesc': 'Try searching with other words',
  'retry': 'Retry',
  'notes': 'Notes',
  'confirmDeleteNote': 'Confirm delete note',
  'pageNumber': 'Page Number',
  'noNotesFound': 'No notes found',
  'recentsRead': 'Recent Reads',
  'home': 'Home',
  'search': 'Search',
  'more': 'More',
  'someThingWentWrong': 'Something went wrong',
  'slowInternetConnection': 'Slow Internet Connection',
  'somethingWentWrongWhileConnectingToServer': 'Something went wrong while connecting to the server',
  'reloadPage': 'Reload Page',
  'contactUs': 'Contact Us',
  'settings': 'Settings',
  'profile': 'Profile',
  'forgetPassword': 'Forgot Password',
  'startSearchMessage': 'Start Search',
  'clear': 'Clear',
  'selectConditionMessage': 'Select condition message',
  'copiedToClipboard': 'Copied to Clipboard',
  'cantReachAppNow': 'Can\'t reach app now',
  'error': 'Error',
  'newUpdateAvailable': 'New update available',
  'language': 'Language',
  'aboutFeatures': 'About Features',
  'startKhatma': 'Start Khatma',
  'todaysWerd': 'Today\'s Werd',
  'doneWerd': 'Completed Werd',
  'days': 'Days',
  'aboutLibrary': 'About Library',
  'contemporary': 'Contemporary',
  'example': 'Example',
  'copiedText': 'Copied Text',
  'exact': 'Exact',
  'defaultz': 'Defaults',
  'phoneValidation': 'Phone Validation',
  'phone': 'Phone',
  'wallet': 'Wallet',
  'about': 'About',
  'alreadyHaveAnAccount': 'Already have an account?',
  'acceptTermsValidation': 'Must agree to terms',
  'agentIdValidation': 'Agent ID Validation',
  'selectAgent': 'Select Agent',
  'editProfile': 'Edit Profile',
  'points': 'Points',
  'openCamera': 'Open Camera',
  'selectPhoto': 'Select Photo',
  'unauthorizedMessage': 'Unauthorized access',
  'termsOfUse': 'Terms of Use',
  'latestProducts': 'Latest Products',
  'rates': 'Rates',
  'noRates': 'No rates available',
  'beTheFirstToRate': 'Be the first to rate',
  'comment': 'Comment',
  'rate': 'Rate',
  'rateThisProduct': 'Rate this product',
  'myPoints': 'My Points',
  'point': 'Point',
  'sendToUser': 'Send to User',
  'transactionsHistory': 'Transaction History',
  'sendPoints': 'Send Points',
  'areYouSureToSendPoints': 'Are you sure to send points?',
  'confirmTransaction': 'Confirm Transaction',
  'pointsSent': 'Points Sent',
  'sendPointsInstructions': 'Send Points Instructions',
  'searchByPhone': 'Search by Phone',
  'noUsersFound': 'No users found',
  'selectUser': 'Select User',
  'userToReceivePoints': 'User to receive points',
  'enterPoints': 'Enter Points',
  'currentPoints': 'Current Points',
  'select': 'Select',

  // Register form translations
  'driverRegistration': 'Driver Registration',
  'companyRegistration': 'Company Registration',
  'createDriverAccount': 'Create your driver account',
  'createCompanyAccount': 'Create your company account',
  'profileImage': 'Profile Image',
  'personalInformation': 'Personal Information',
  'fullName': 'Full Name',
  'username': 'Username',
  'locationInformation': 'Location Information',
  'selectCity': 'Select City',
  'addressOptional': 'Address (Optional)',
  'vehicleInformation': 'Vehicle Information',
  'selectTruckType': 'Select Truck Type',
  'selectTruckSize': 'Select Truck Size',
  'vehicleDocuments': 'Vehicle Documents',
  'carLicense': 'Car License',
  'carImage': 'Car Image',
  'security': 'Security',
  'confirmPassword': 'Confirm Password',
  'registerAsDriver': 'Register as Driver',
  'companyLogo': 'Company Logo',
  'companyInformation': 'Company Information',
  'companyName': 'Company Name',
  'companyType': 'Company Type',
  'commercialNumberOptional': 'Commercial Number (Optional)',
  'websiteOptional': 'Website (Optional)',
  'accountInformation': 'Account Information',
  'contactPerson': 'Contact Person',
  'contactPersonName': 'Contact Person Name',
  'contactPhone': 'Contact Phone',
  'contactEmail': 'Contact Email',
  'registerCompany': 'Register Company',
  'tapToSelect': 'Tap to select',
  'step': 'Step',
  'of': 'of',
  'next': 'Next',
  'previous': 'Previous',
  'pleaseEnterFullName': 'Please enter full name',
  'pleaseEnterUsername': 'Please enter username',
  'pleaseEnterPhoneNumber': 'Please enter phone number',
  'pleaseSelectCity': 'Please select city',
  'pleaseSelectTruckType': 'Please select truck type',
  'pleaseSelectTruckSize': 'Please select truck size',
  'passwordMustBeBetween': 'Password must be between 6-30 characters',
  'passwordsDoNotMatch': 'Passwords do not match',
  'pleaseEnterCompanyName': 'Please enter company name',
  'pleaseEnterCompanyType': 'Please enter company type',
  'pleaseEnterContactPersonName': 'Please enter contact person name',
  'pleaseEnterContactPhone': 'Please enter contact phone',
  'pleaseEnterContactEmail': 'Please enter contact email',
  'pleaseSelectAllRequiredImages': 'Please select all required images',
  'pleaseSelectCompanyImage': 'Please select company image',
  'usernameValidation': 'Please enter username',
  'cityValidation': 'Please select city',
  'truckTypeValidation': 'Please select truck type',
  'truckSizeValidation': 'Please select truck size',
  'companyNameValidation': 'Please enter company name',
  'companyTypeValidation': 'Please enter company type',
  'contactPersonValidation': 'Please enter contact person',
  'contactPhoneValidation': 'Please enter contact phone',
  'contactEmailValidation': 'Please enter contact email',
  'imagesValidation': 'Please select all required images',
  'companyImageValidation': 'Please select company image',

  // Map picker translations
  'selectLocationOnMap': 'Select Location on Map',
  'mapPicker': 'Map Picker',
  'currentLocation': 'Current Location',
  'searchLocation': 'Search Location',
  'confirmLocation': 'Confirm Location',
  'loadingMap': 'Loading Map...',
  'locationPermissionDenied': 'Location permission denied',
  'locationServiceDisabled': 'Location service disabled',
  'failedToGetLocation': 'Failed to get location',
  'tapToSelectLocation': 'Tap to select location on map',

  // Profile and Orders translations
  'profileUpdatedSuccessfully': 'Profile updated successfully',
  'passwordChangedSuccessfully': 'Password changed successfully',
  'orderAcceptedSuccessfully': 'Order accepted successfully',
  'orderCreatedSuccessfully': 'Order created successfully',
  'orderUpdatedSuccessfully': 'Order updated successfully',
  'orderDeletedSuccessfully': 'Order deleted successfully',
  'orderStatusUpdatedSuccessfully': 'Order status updated successfully',
  'orderTrackedSuccessfully': 'Order tracked successfully',
  'balanceRechargedSuccessfully': 'Balance recharged successfully',
  'driverProfile': 'Driver Profile',
  'companyProfile': 'Company Profile',
  'myOrders': 'My Orders',
  'availableOrders': 'Available Orders',
  'orderDetails': 'Order Details',
  'trackOrder': 'Track Order',
  'acceptOrder': 'Accept Order',
  'orderStatus': 'Order Status',
  'balance': 'Balance',
  'rechargeBalance': 'Recharge Balance',
  'amount': 'Amount',
  'truckType': 'Truck Type',
  'truckSize': 'Truck Size',
  'vehicleLicense': 'Vehicle License',
  'vehicleImage': 'Vehicle Image',
  'companyNameField': 'Company Name',
  'companyTypeField': 'Company Type',
  'commercialNumber': 'Commercial Number',
  'contactPersonField': 'Contact Person',
  'contactPhoneField': 'Contact Phone',
  'contactEmailField': 'Contact Email',
  'website': 'Website',
  'startDate': 'Start Date',
  'endDate': 'End Date',
  'weight': 'Weight',
  'startLocation': 'Start Location',
  'endLocation': 'End Location',
  'description': 'Description',
  'driverName': 'Driver Name',
  'orderNumber': 'Order Number',
  'price': 'Price',
  'createdAt': 'Created At',
  'noOrdersFound': 'No Orders Found',
  'noOrdersFoundDesc': 'No orders found yet',
  'serviceInformation': 'Service Information',

  // New navigation translations
  'drivers': 'Drivers',
  'companyDashboard': 'Company Dashboard',
  'driverDashboard': 'Driver Dashboard',
  'totalOrders': 'Total Orders',
  'activeOrders': 'Active Orders',
  'completedOrders': 'Completed Orders',
  'totalDrivers': 'Total Drivers',
  'currentBalance': 'Current Balance',
  'todayEarnings': 'Today\'s Earnings',
  'driverStatus': 'Driver Status',
  'available': 'Available',
  'busy': 'Busy',
  'offline': 'Offline',
  'goOnline': 'Go Online',
  'goOffline': 'Go Offline',
  'quickActions': 'Quick Actions',
  'recentActivity': 'Recent Activity',
  'recentOrders': 'Recent Orders',
  'overview': 'Overview',
  'orderStatistics': 'Order Statistics',
  'welcomeBack': 'Welcome Back!',
  'manageYourFleet': 'Manage your fleet and orders efficiently',
  'readyToAcceptOrders': 'Ready to accept new orders',
  'currentlyBusyWithOrders': 'Currently busy with orders',
  'createOrder': 'Create Order',
  'viewDrivers': 'View Drivers',
  'assignOrder': 'Assign Order',
  'viewDetails': 'View Details',
  'driverDetails': 'Driver Details',
  'location': 'Location',
  'rating': 'Rating',
  'searchDrivers': 'Search drivers...',
  'noDriversFound': 'No drivers found',
  'newOrderCreated': 'New order created',
  'driverAssigned': 'Driver assigned',
  'orderCompleted': 'Order completed',
  'hoursAgo': 'hours ago',
  'inProgress': 'In Progress',
  'completed': 'Completed',
  'changeOrderStatus': 'Change Order Status',
  'selectOrderStatus': 'Select Order Status',
  'notesRequired': 'Notes are required for this status',
  'enterNotesForStatus': 'Enter notes for this status change',
  'updateStatus': 'Update Status',

  // Create Order translations
  'createNewOrder': 'Create New Order',
  'orderInformation': 'Order Information',
  'locationInformationStep': 'Location Information',
  'truckInformation': 'Truck Information',
  'finish': 'Finish',
  'selectStartDate': 'Select Start Date',
  'selectEndDate': 'Select End Date',
  'selectStartTime': 'Select Start Time',
  'selectEndTime': 'Select End Time',
  'startDateTime': 'Start Date & Time',
  'endDateTime': 'End Date & Time',
  'weightKg': 'Weight (kg)',
  'enterWeight': 'Enter weight in kg',
  'orderDescription': 'Order Description',
  'enterDescription': 'Enter order description',
  'orderNotes': 'Order Notes',
  'enterNotes': 'Enter additional notes',
  'startLocationInfo': 'Start Location',
  'endLocationInfo': 'End Location',
  'selectStartLocation': 'Select Start Location',
  'selectEndLocation': 'Select End Location',
  'startAddressField': 'Start Address',
  'endAddressField': 'End Address',
  'editAddress': 'Edit Address',
  'selectDriver': 'Select Driver (Optional)',
  'noDriverSelected': 'No driver selected',
  'orderCreatedSuccessMessage': 'Order has been created successfully!',
  'goBack': 'Go Back',
  'pleaseSelectStartDate': 'Please select start date',
  'pleaseSelectEndDate': 'Please select end date',
  'pleaseEnterWeight': 'Please enter weight',
  'pleaseSelectStartLocation': 'Please select start location',
  'pleaseSelectEndLocation': 'Please select end location',
  'pleaseSelectTruckTypeOrder': 'Please select truck type',
  'pleaseSelectTruckSizeOrder': 'Please select truck size',

  // Trip related translations
  'tripStart': 'Trip Start',
  'tripDetails': 'Trip Details',
  'destination': 'Destination',
  'routeToDestination': 'Route to Destination',
  'showTripDetails': 'Show Trip Details',
  'hideTripDetails': 'Hide Trip Details',
  'locationPermissionRequired': 'Location permission required',
  'enableLocationServices': 'Enable location services',
  'gettingCurrentLocation': 'Getting current location...',
  'failedToGetCurrentLocation': 'Failed to get current location',
  'startTrip': 'Start Trip',
  'tripInProgress': 'Trip in Progress',
  'weightMustBeGreaterThanZero': 'Weight must be greater than zero',
  'endDateMustBeAfterStartDate': 'End date must be after start date',
  'tapToSelectLocationOnMapOrder': 'Tap to select location on map',
  'selectedLocation': 'Selected Location',
};
