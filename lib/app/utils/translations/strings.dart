import 'package:get/get.dart';

class Strings {
  String appName = "حمولتي";
  String truckDetails = "truckDetails".tr;
  String orderDetailsNotAvailable = "orderDetailsNotAvailable".tr;
  String viewDetails = "viewDetails".tr;
  String driverDetails = "driverDetails".tr;
  String noDriversFound = "noDriversFound".tr;
  String rating = "rating".tr;
  String location = "location".tr;
  String status = "status".tr;
  String companyDrivers = "companyDrivers".tr;
  String showAll = "showAll".tr;
  String theoffers = "theoffers".tr;
  String showDetails = "showDetails".tr;
  String tripDestination = "tripDestination".tr;
  String selectCompanyOptional = "selectCompanyOptional".tr;
  String recentOrders = "recentOrders".tr;
  String amountIQD = "amountIQD".tr;
  String minimumRechargeAmount = "minimumRechargeAmount".tr;
  String pleaseEnterAmount = "pleaseEnterAmount".tr;
  String pleaseEnterValidAmount = "pleaseEnterValidAmount".tr;
  String recharge = "recharge".tr;
  String account = "account".tr;
  String supportAndSharing = "supportAndSharing".tr;
  String aboutAndLegal = "aboutAndLegal".tr;
  String orders = "orders".tr;
  String ordersStatistics = "ordersStatistics".tr;
  String totalOrders = "totalOrders".tr;
  String activeOrders = "activeOrders".tr;
  String completeOrders = "completeOrders".tr;
  String endTime = "endTime".tr;
  String startTime = "startTime".tr;
  String city = "city".tr;
  String edit = "edit".tr;
  String drivers = "drivers".tr;
  String address = "address".tr;
  String orderNow = "orderNow".tr;
  String areYouSureYouWantToRequestThisGift = "areYouSureYouWantToRequestThisGift".tr;
  String requestGift = "requestGift".tr;
  String loginAsGuest = "loginAsGuest".tr;
  String dismiss = "dismiss".tr;
  String products = "products".tr;
  String gifts = "gifts".tr;
  String myGifts = "myGifts".tr;
  String changeLanguage = "changeLanguage".tr;
  String highlight = 'highlight'.tr;
  String highlights = 'highlights'.tr;
  String notifications = 'notifications'.tr;
  String editData = 'editData'.tr;
  String dontHaveAnAccount = 'dontHaveAnAccount'.tr;
  String and = 'and'.tr;
  String paste = 'paste'.tr;
  String iAgreeTo = 'iAgreeTo'.tr;
  String termsAndConditions = 'termsAndConditions'.tr;
  String privacyPolicy = 'privacyPolicy'.tr;
  String privacyPolicyAndTerms = 'privacyPolicyAndTerms'.tr;
  String cut = 'cut'.tr;
  String update = 'update'.tr;
  String linesHeight = 'linesHeight'.tr;
  String all = 'all'.tr;
  String pleaseWaitUntrilSearchSyncFinished = 'pleaseWaitUntrilSearchSyncFinished'.tr;
  String checkDisplaySetting = 'checkDisplaySetting'.tr;
  String settingDisplay = 'settingDisplay'.tr;
  String myFiles = 'myFiles'.tr;
  String objectiveIndex = 'objectiveIndex'.tr;
  String to = 'to'.tr;
  String from = 'from'.tr;
  String pages = 'pages'.tr;
  String delete = 'delete'.tr;
  String selectAll = 'selectAll'.tr;
  String writeSeachWordHere = 'writeSeachWordHere'.tr;
  String pressAgainToExit = 'pressAgainToExit'.tr;
  String couldntOpenUrl = 'couldntOpenUrl'.tr;
  String rateApp = 'rateApp'.tr;
  String shareApp = 'shareApp'.tr;
  String shareAppMessage = 'shareAppMessage'.tr;
  String forgetPasswordSuccessMessage = 'forgetPasswordSuccessMessage'.tr;
  String noResultFound = 'noResultFound'.tr;
  String writeAtLeast3Letters = 'writeAtLeast3Letters'.tr;
  String writeAtLeast10Letters = 'writeAtLeast10Letters'.tr;
  String theAuthor = 'theAuthor'.tr;

  String receiveNotifications = 'receiveNotifications'.tr;
  String nightMode = 'nightMode'.tr;
  String fontColor = 'fontColor'.tr;
  String fontType = 'fontType'.tr;
  String fontSize = 'fontSize'.tr;
  String refresh = 'refresh'.tr;
  String selectWidget = 'selectWidget'.tr;
  String selectView = 'selectView'.tr;
  String selectComponent = 'selectComponent'.tr;
  String copy = 'copy'.tr;
  String share = 'share'.tr;
  String message = 'message'.tr;
  String emailOptional = 'emailOptional'.tr;
  String nameOptional = 'nameOptional'.tr;
  String contactUsNote = 'contactUsNote'.tr;
  String cancel = 'cancel'.tr;
  String confirm = 'confirm'.tr;
  String logoutMessage = 'logoutMessage'.tr;
  String deleteMessage = 'deleteMessage'.tr;
  String logout = 'logout'.tr;
  String deleteAccount = 'deleteAccount'.tr;
  String agree = 'agree'.tr;
  String send = 'send'.tr;
  String successSendResetPasswordMessage = 'successSendResetPasswordMessage'.tr;
  String resetPasswordMessage = 'resetPasswordMessage'.tr;
  String resetPassword = 'resetPassword'.tr;
  String name = 'name'.tr;
  String register = 'register'.tr;
  String createAccount = 'createAccount'.tr;
  String registerNew = 'registerNew'.tr;
  String nameValidation = 'nameValidation'.tr;
  String passwordConfirmvalidation = 'passwordConfirmvalidation'.tr;
  String passwordConfirm = 'passwordConfirm'.tr;
  String changePassword = 'changePassword'.tr;
  String editPassword = 'editPassword'.tr;
  String forgetPassword2 = 'forgetPassword2'.tr;
  String password = 'password'.tr;
  String currentPssword = 'currentPssword'.tr;
  String newPassword = 'newPassword'.tr;
  String configmNewPassword = 'configmNewPassword'.tr;
  String passwordValidation = 'passwordValidation'.tr;
  String close = 'close'.tr;
  String email = 'email'.tr;
  String newAccount = 'newAccount'.tr;
  String emailValidation = 'emailValidation'.tr;
  String empty = 'empty'.tr;
  String searchForTitleOrAuthor = 'searchForTitleOrAuthor'.tr;
  String notification = 'notification'.tr;
  String noInternetConnection = 'noInternetConnection'.tr;
  String serverError = 'serverError'.tr;
  String requireLoginMessage = 'requireLoginMessage'.tr;
  String login = 'login'.tr;
  String emptySearchMessage = 'emptySearchMessage'.tr;
  String emptySearchMessageDesc = 'emptySearchMessageDesc'.tr;
  String retry = 'retry'.tr;
  String notes = 'notes'.tr;
  String confirmDeleteNote = 'confirmDeleteNote'.tr;
  String pageNumber = 'pageNumber'.tr;
  String noNotesFound = 'noNotesFound'.tr;
  String recentsRead = 'recentsRead'.tr;
  String home = 'home'.tr;
  String search = 'search'.tr;
  String more = 'more'.tr;
  String someThingWentWrong = 'someThingWentWrong'.tr;
  String slowInternetConnection = 'slowInternetConnection'.tr;
  String somethingWentWrongWhileConnectingToServer = 'somethingWentWrongWhileConnectingToServer'.tr;
  String reloadPage = 'reloadPage'.tr;
  String contactUs = 'contactUs'.tr;
  String settings = 'settings'.tr;
  String profile = 'profile'.tr;
  String forgetPassword = 'forgetPassword'.tr;
  String startSearchMessage = "startSearchMessage".tr;
  String clear = 'clear'.tr;
  String selectConditionMessage = "selectConditionMessage".tr;
  String copiedToClipboard = "copiedToClipboard".tr;
  String cantReachAppNow = 'cantReachAppNow'.tr;
  String error = 'error'.tr;
  String newUpdateAvailable = "newUpdateAvailable".tr;
  String language = 'language'.tr;
  String aboutFeatures = 'aboutFeatures'.tr;
  String startKhatma = 'startKhatma'.tr;
  String todaysWerd = 'todaysWerd'.tr;
  String doneWerd = 'doneWerd'.tr;
  String days = 'days'.tr;
  String aboutLibrary = 'aboutLibrary'.tr;
  String contemporary = 'contemporary'.tr;
  String example = 'example'.tr;
  String copiedText = 'copiedText'.tr;
  String exact = 'exact'.tr;
  String defaultz = 'defaultz'.tr;
  String phoneValidation = 'phoneValidation'.tr;
  String phone = 'phone'.tr;
  String wallet = 'wallet'.tr;
  String alreadyHaveAnAccount = 'alreadyHaveAnAccount'.tr;
  String about = 'about'.tr;
  String acceptTermsValidation = 'acceptTermsValidation'.tr;
  String agentIdValidation = 'agentIdValidation'.tr;
  String selectAgent = 'selectAgent'.tr;
  String editProfile = 'editProfile'.tr;
  String points = 'points'.tr;
  String openCamera = 'openCamera'.tr;
  String selectPhoto = 'selectPhoto'.tr;
  String unauthorizedMessage = 'unauthorizedMessage'.tr;
  String termsOfUse = 'termsOfUse'.tr;
  String latestProducts = 'latestProducts'.tr;
  String rates = 'rates'.tr;
  String noRates = 'noRates'.tr;
  String beTheFirstToRate = 'beTheFirstToRate'.tr;
  String comment = 'comment'.tr;
  String rate = 'rate'.tr;
  String rateThisProduct = 'rateThisProduct'.tr;
  String myPoints = 'myPoints'.tr;
  String point = 'point'.tr;
  String sendToUser = 'sendToUser'.tr;
  String transactionsHistory = 'transactionsHistory'.tr;
  String sendPoints = 'sendPoints'.tr;
  String areYouSureToSendPoints = 'areYouSureToSendPoints'.tr;
  String confirmTransaction = 'confirmTransaction'.tr;
  String pointsSent = 'pointsSent'.tr;
  String sendPointsInstructions = 'sendPointsInstructions'.tr;
  String searchByPhone = 'searchByPhone'.tr;
  String noUsersFound = 'noUsersFound'.tr;
  String selectUser = 'selectUser'.tr;
  String userToReceivePoints = 'userToReceivePoints'.tr;
  String enterPoints = 'enterPoints'.tr;
  String currentPoints = 'currentPoints'.tr;
  String select = 'select'.tr;
  String welcomeMessage = 'welcomeMessage'.tr;

  // Profile and Orders translations
  String profileUpdatedSuccessfully = 'profileUpdatedSuccessfully'.tr;
  String passwordChangedSuccessfully = 'passwordChangedSuccessfully'.tr;
  String orderAcceptedSuccessfully = 'orderAcceptedSuccessfully'.tr;
  String orderCreatedSuccessfully = 'orderCreatedSuccessfully'.tr;
  String orderUpdatedSuccessfully = 'orderUpdatedSuccessfully'.tr;
  String orderDeletedSuccessfully = 'orderDeletedSuccessfully'.tr;
  String orderStatusUpdatedSuccessfully = 'orderStatusUpdatedSuccessfully'.tr;
  String orderTrackedSuccessfully = 'orderTrackedSuccessfully'.tr;
  String balanceRechargedSuccessfully = 'balanceRechargedSuccessfully'.tr;
  String driverProfile = 'driverProfile'.tr;
  String companyProfile = 'companyProfile'.tr;
  String myOrders = 'myOrders'.tr;
  String availableOrders = 'availableOrders'.tr;
  String orderDetails = 'orderDetails'.tr;
  String trackOrder = 'trackOrder'.tr;
  String acceptOrder = 'acceptOrder'.tr;
  String orderStatus = 'orderStatus'.tr;
  String balance = 'balance'.tr;
  String rechargeBalance = 'rechargeBalance'.tr;
  String amount = 'amount'.tr;
  String truckType = 'truckType'.tr;
  String truckSize = 'truckSize'.tr;
  String vehicleLicense = 'vehicleLicense'.tr;
  String vehicleImage = 'vehicleImage'.tr;
  String companyNameField = 'companyNameField'.tr;
  String companyTypeField = 'companyTypeField'.tr;
  String commercialNumber = 'commercialNumber'.tr;
  String contactPersonField = 'contactPersonField'.tr;
  String contactPhoneField = 'contactPhoneField'.tr;
  String contactEmailField = 'contactEmailField'.tr;
  String website = 'website'.tr;
  String startDate = 'startDate'.tr;
  String endDate = 'endDate'.tr;
  String weight = 'weight'.tr;
  String startLocation = 'startLocation'.tr;
  String endLocation = 'endLocation'.tr;
  String description = 'description'.tr;
  String driverName = 'driverName'.tr;
  String orderNumber = 'orderNumber'.tr;
  String price = 'price'.tr;
  String createdAt = 'createdAt'.tr;
  String noOrdersFound = 'noOrdersFound'.tr;
  String noOrdersFoundDesc = 'noOrdersFoundDesc'.tr;
  String serviceInformation = 'serviceInformation'.tr;

  // Register form translations
  String driverRegistration = 'driverRegistration'.tr;
  String companyRegistration = 'companyRegistration'.tr;
  String createDriverAccount = 'createDriverAccount'.tr;
  String createCompanyAccount = 'createCompanyAccount'.tr;
  String profileImage = 'profileImage'.tr;
  String personalInformation = 'personalInformation'.tr;
  String fullName = 'fullName'.tr;
  String username = 'username'.tr;
  String locationInformation = 'locationInformation'.tr;
  String selectCity = 'selectCity'.tr;
  String addressOptional = 'addressOptional'.tr;
  String vehicleInformation = 'vehicleInformation'.tr;
  String selectTruckType = 'selectTruckType'.tr;
  String selectTruckSize = 'selectTruckSize'.tr;
  String vehicleDocuments = 'vehicleDocuments'.tr;

  // Create Order translations
  String createNewOrder = 'createNewOrder'.tr;
  String orderInformation = 'orderInformation'.tr;
  String locationInformationStep = 'locationInformationStep'.tr;
  String truckInformation = 'truckInformation'.tr;
  String step = 'step'.tr;
  String of = 'of'.tr;
  String next = 'next'.tr;
  String previous = 'previous'.tr;
  String finish = 'finish'.tr;
  String selectStartDate = 'selectStartDate'.tr;
  String selectEndDate = 'selectEndDate'.tr;
  String selectStartTime = 'selectStartTime'.tr;
  String selectEndTime = 'selectEndTime'.tr;
  String startDateTime = 'startDateTime'.tr;
  String endDateTime = 'endDateTime'.tr;
  String weightKg = 'weightKg'.tr;
  String enterWeight = 'enterWeight'.tr;
  String orderDescription = 'orderDescription'.tr;
  String enterDescription = 'enterDescription'.tr;
  String orderNotes = 'orderNotes'.tr;
  String enterNotes = 'enterNotes'.tr;
  String startLocationInfo = 'startLocationInfo'.tr;
  String endLocationInfo = 'endLocationInfo'.tr;
  String selectStartLocation = 'selectStartLocation'.tr;
  String selectEndLocation = 'selectEndLocation'.tr;
  String startAddressField = 'startAddressField'.tr;
  String endAddressField = 'endAddressField'.tr;
  String editAddress = 'editAddress'.tr;
  String selectDriver = 'selectDriver'.tr;
  String noDriverSelected = 'noDriverSelected'.tr;
  String orderCreatedSuccessMessage = 'orderCreatedSuccessMessage'.tr;
  String goBack = 'goBack'.tr;
  String pleaseSelectStartDate = 'pleaseSelectStartDate'.tr;
  String pleaseSelectEndDate = 'pleaseSelectEndDate'.tr;
  String changeOrderStatus = 'changeOrderStatus'.tr;
  String selectOrderStatus = 'selectOrderStatus'.tr;
  String notesRequired = 'notesRequired'.tr;
  String enterNotesForStatus = 'enterNotesForStatus'.tr;
  String updateStatus = 'updateStatus'.tr;
  String pleaseEnterWeight = 'pleaseEnterWeight'.tr;
  String pleaseSelectStartLocation = 'pleaseSelectStartLocation'.tr;
  String pleaseSelectEndLocation = 'pleaseSelectEndLocation'.tr;
  String pleaseSelectTruckTypeOrder = 'pleaseSelectTruckTypeOrder'.tr;
  String pleaseSelectTruckSizeOrder = 'pleaseSelectTruckSizeOrder'.tr;
  String weightMustBeGreaterThanZero = 'weightMustBeGreaterThanZero'.tr;
  String endDateMustBeAfterStartDate = 'endDateMustBeAfterStartDate'.tr;
  String tapToSelectLocationOnMapOrder = 'tapToSelectLocationOnMapOrder'.tr;
  String selectedLocation = 'selectedLocation'.tr;
  String carLicense = 'carLicense'.tr;
  String carImage = 'carImage'.tr;
  String security = 'security'.tr;
  String confirmPassword = 'confirmPassword'.tr;
  String registerAsDriver = 'registerAsDriver'.tr;
  String companyLogo = 'companyLogo'.tr;
  String companyInformation = 'companyInformation'.tr;
  String companyName = 'companyName'.tr;
  String companyType = 'companyType'.tr;
  String commercialNumberOptional = 'commercialNumberOptional'.tr;
  String websiteOptional = 'websiteOptional'.tr;
  String accountInformation = 'accountInformation'.tr;
  String contactPerson = 'contactPerson'.tr;
  String contactPersonName = 'contactPersonName'.tr;
  String contactPhone = 'contactPhone'.tr;
  String contactEmail = 'contactEmail'.tr;
  String registerCompany = 'registerCompany'.tr;
  String tapToSelect = 'tapToSelect'.tr;
  String pleaseEnterFullName = 'pleaseEnterFullName'.tr;
  String pleaseEnterUsername = 'pleaseEnterUsername'.tr;
  String pleaseEnterPhoneNumber = 'pleaseEnterPhoneNumber'.tr;
  String pleaseSelectCity = 'pleaseSelectCity'.tr;
  String pleaseSelectTruckType = 'pleaseSelectTruckType'.tr;
  String pleaseSelectTruckSize = 'pleaseSelectTruckSize'.tr;
  String passwordMustBeBetween = 'passwordMustBeBetween'.tr;
  String passwordsDoNotMatch = 'passwordsDoNotMatch'.tr;
  String pleaseEnterCompanyName = 'pleaseEnterCompanyName'.tr;
  String pleaseEnterCompanyType = 'pleaseEnterCompanyType'.tr;
  String pleaseEnterContactPersonName = 'pleaseEnterContactPersonName'.tr;
  String pleaseEnterContactPhone = 'pleaseEnterContactPhone'.tr;
  String pleaseEnterContactEmail = 'pleaseEnterContactEmail'.tr;
  String pleaseSelectAllRequiredImages = 'pleaseSelectAllRequiredImages'.tr;
  String pleaseSelectCompanyImage = 'pleaseSelectCompanyImage'.tr;
  String usernameValidation = 'usernameValidation'.tr;
  String cityValidation = 'cityValidation'.tr;
  String truckTypeValidation = 'truckTypeValidation'.tr;
  String truckSizeValidation = 'truckSizeValidation'.tr;
  String companyNameValidation = 'companyNameValidation'.tr;
  String companyTypeValidation = 'companyTypeValidation'.tr;
  String contactPersonValidation = 'contactPersonValidation'.tr;
  String contactPhoneValidation = 'contactPhoneValidation'.tr;
  String contactEmailValidation = 'contactEmailValidation'.tr;
  String imagesValidation = 'imagesValidation'.tr;
  String companyImageValidation = 'companyImageValidation'.tr;

  // Map picker translations
  String selectLocationOnMap = 'selectLocationOnMap'.tr;
  String mapPicker = 'mapPicker'.tr;
  String currentLocation = 'currentLocation'.tr;
  String searchLocation = 'searchLocation'.tr;
  String confirmLocation = 'confirmLocation'.tr;
  String loadingMap = 'loadingMap'.tr;
  String locationPermissionDenied = 'locationPermissionDenied'.tr;
  String locationServiceDisabled = 'locationServiceDisabled'.tr;
  String failedToGetLocation = 'failedToGetLocation'.tr;
  String tapToSelectLocation = 'tapToSelectLocation'.tr;

  // Trip related translations
  String tripStart = 'tripStart'.tr;
  String tripDetails = 'tripDetails'.tr;
  String destination = 'destination'.tr;
  String routeToDestination = 'routeToDestination'.tr;
  String showTripDetails = 'showTripDetails'.tr;
  String hideTripDetails = 'hideTripDetails'.tr;
  String locationPermissionRequired = 'locationPermissionRequired'.tr;
  String enableLocationServices = 'enableLocationServices'.tr;
  String gettingCurrentLocation = 'gettingCurrentLocation'.tr;
  String failedToGetCurrentLocation = 'failedToGetCurrentLocation'.tr;
  String startTrip = 'startTrip'.tr;
  String tripInProgress = 'tripInProgress'.tr;
  String routeInformation = 'routeInformation'.tr;
  String additionalInformation = 'additionalInformation'.tr;
  String loading = 'loading'.tr;


}
