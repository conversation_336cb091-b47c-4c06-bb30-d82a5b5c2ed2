import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

Future<String> getInfoDeviceId() async {
  String deviceId = '';
  String deviceId2 = '';
  String deviceId3 = '';
  if(kDebugMode) return '123456jdkjshjkshfiuehrihiad'; 
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

  if (Platform.isAndroid) {
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    deviceId = androidInfo.id;
    deviceId2 = androidInfo.id;
    deviceId3 = androidInfo.device;
  } else {
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    deviceId2 = '${iosInfo.utsname.machine}-${iosInfo.systemName}';
    deviceId3 = iosInfo.identifierForVendor ?? '';
  }
  return '$deviceId-$deviceId2-$deviceId3';
}

Future<String> getInfoModel() async {
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  String model = "";

  if (Platform.isAndroid) {
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    model = androidInfo.model;
  } else {
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    model = iosInfo.model;
    if (model.isEmpty) model = iosInfo.name;
    if (model.isEmpty) model = "NA";
  }
  return model;
}

Future<String> getInfoBrand() async {
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  String brand = "";

  if (Platform.isAndroid) {
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    brand = androidInfo.brand;
  } else {
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    brand = iosInfo.systemName;
    if (brand.isEmpty) brand = iosInfo.systemName;
    if (brand.isEmpty) brand = "NA";
  }
  return brand;
}


actionOpenLocation(String? coordination) async {
  if (coordination == null) return;
  List<String> list = coordination.split(',');
  if (list.length != 2) return;
  String url = 'https://www.google.com/maps/search/?api=1&query=${list[0]},${list[1]}';
  Uri uri = Uri.parse(url);
  bool canLaunch = await canLaunchUrl(uri);
  if (canLaunch) await launchUrl(uri);
}

getColorAccordingToName(String name) {
  if (name.isEmpty) return Colors.red;
  switch (name[0].toUpperCase()) {
    case "A":
      return Colors.red;
    case "B":
      return Colors.green;
    case "C":
      return Colors.blue;
    case "D":
      return Colors.yellow[800];
    case "E":
      return Colors.purple;
    case "F":
      return Colors.orange;
    case "G":
      return Colors.pink;
    case "H":
      return Colors.brown;
    case "I":
      return Colors.teal;
    case "J":
      return Colors.cyan;
    case "K":
      return Colors.lime;
    case "L":
      return Colors.indigo;
    case "M":
      return Colors.amber;
    case "N":
      return Colors.deepOrange;
    case "O":
      return Colors.deepPurple;
    case "P":
      return Colors.lightBlue;
    case "Q":
      return Colors.lightGreen;
    case "R":
      return Colors.grey;
    case "S":
      return Colors.black;
    case "T":
      return Colors.white;
    case "U":
      return Colors.blueGrey;
    case "V":
      return Colors.brown;
    case "W":
      return Colors.cyanAccent;
    case "X":
      return Colors.deepOrangeAccent;
    case "Y":
      return Colors.deepPurpleAccent;
    case "Z":
      return Colors.greenAccent;
    case "ا":
      return Colors.red;
    case "ب":
      return Colors.green;
    case "ت":
      return Colors.blue;
    case "ث":
      return Colors.yellow[800];
    case "ج":
      return Colors.purple;
    case "ح":
      return Colors.orange;
    case "خ":
      return Colors.pink;
    case "د":
      return Colors.brown;
    case "ذ":
      return Colors.teal;
    case "ر":
      return Colors.cyan;
    case "ز":
      return Colors.lime;
    case "س":
      return Colors.indigo;
    case "ش":
      return Colors.amber;
    case "ص":
      return Colors.deepOrange;
    case "ض":
      return Colors.deepPurple;
    case "ط":
      return Colors.lightBlue;
    case "ظ":
      return Colors.lightGreen;
    case "ع":
      return Colors.grey;
    case "غ":
      return Colors.black;
    case "ف":
      return Colors.white;
    case "ق":
      return Colors.blueGrey;
    case "ك":
      return Colors.brown;
    case "ل":
      return Colors.cyanAccent;
    case "م":
      return Colors.deepOrangeAccent;
    case "ن":
      return Colors.deepPurpleAccent;
    case "ه":
      return Colors.greenAccent;
    case "و":
      return Colors.red;
    case "ي":
      return Colors.green;
    case "ء":
      return Colors.blue;
    case "آ":
      return Colors.yellow[800];
    case "أ":
      return Colors.purple;
    case "ؤ":
      return Colors.orange;
    case "إ":
      return Colors.pink;
    case "ئ":
      return Colors.brown;
    case "ة":
      return Colors.cyan;
    case "ى":
    default:
      return Colors.red;
  }
}

String convertNameToShort(String name) {
  List<String> names = name.split(' ');
  String shortName = '';
  for (var element in names) {
    String word = element.trim();
    if (word.isEmpty) continue;
    if (shortName.length > 2) break;
    shortName += shortName.isEmpty ? element[0] : '.${element[0]}';
  }
  return shortName;
}
