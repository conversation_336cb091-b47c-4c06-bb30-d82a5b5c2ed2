import 'package:app/app/controllers/global_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppColor {
  static Clr primary = ColorModel(Color(0xff23A2B3), Color(0xffA0CAFD));
  static Clr primaryDark = ColorModel(Color(0xFFBD300D), Color(0xff9ECBFF));
  static Clr primaryColorLight = ColorModel(Color(0xFF0050D9), Color(0xffBBC7DB));
  static Clr onprimary = ColorModel(Color(0xffFFFFFF), Color(0xff003258));
  static Clr primarycontainer = ColorModel(Color(0xFF0050D9), Color(0xffBBC7DB));
  static Clr secondary = ColorModel(Color(0xFF0050D9), Color(0xffBBC7DB));
  static Clr onsecondary = ColorModel(Color(0xffFFFFFF), Color(0xff253140));
  static Clr surface = ColorModel(Color(0xffECEEF4), Color(0xff1D2024));
  static Clr divider = ColorModel(Color(0xffD5D8DF), Color(0xff33363C));
  static Clr background = ColorModel(Color(0xffF8F9FF), Color(0xff111418));
  static Clr appBarColor = ColorModel(Color(0xffFDFCFF), Color(0xff1A1712));
  static Clr icons = ColorModel(Color(0xff3D3C3A), Color(0xffFFFFFF));
  static Clr onIcon = ColorModel(Color(0xffA5ACB9), Color(0xff748398));
  static Clr iconsColorDark = ColorModel(Color(0xff43474E), Color(0xffC3C7CF));
  static Clr error = ColorModel(Color(0xffBA1A1A), Color(0xffFFB4AB));
  static Clr onerror = ColorModel(Color(0xffFFFFFF), Color(0xff690005));
  static Clr textPrimary = ColorModel(Color(0xff43474E), Color(0xffE2E2E6));
  static Clr white = ColorModel(Colors.white, Colors.black87);
  static Clr black = ColorModel(Color(0xff191C20), Colors.white);
  static Clr onbackground = ColorModel(Color(0xff191C20), Colors.white);
  static Clr search = ColorModel(Color(0xffE6E8EE), Color(0xff272A2F));
  static Clr filled = ColorModel(Color(0xffDDDEE4), Color(0xff2A2D31));
  static Clr onFilled = ColorModel(Color(0xffA6A8AE), Color(0xff6D6F73));
  static Clr shadow = ColorModel(Color(0xff00000040), Color(0xff00000040));
  static Clr marker = ColorModel(Color(0xffFEDD00), Color(0xffFEDD00));
  static Clr onprimarycontainer = ColorModel(Color(0xffF0F1F3), Color(0xff111214));

  /* -------------------------------------------------------------------------- */

  static Color clr(Clr color) {
    GlobalController globalController = Get.find();
    bool isDark = globalController.isDarkMode.value;
    if (isDark) {
      return color.darkColor;
    } else {
      return color.lightColor;
    }
  }
}

typedef Clr = ColorModel;

class ColorModel {
  Color lightColor;
  Color darkColor;

  ColorModel(this.lightColor, this.darkColor);
}
