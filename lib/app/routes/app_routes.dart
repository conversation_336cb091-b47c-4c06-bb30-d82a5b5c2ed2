part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const SPLASH = _Paths.SPLASH;
  static const SETTING = _Paths.SETTING;
  static const Page = _Paths.Page;
  static const LAYOUT = _Paths.LAYOUT;
  static const UPDATE_APP = _Paths.UPDATE_APP;
  static const LOGIN = _Paths.LOGIN;
  static const REGISTER = _Paths.REGISTER;
  static const FORGET_PASSWORD = _Paths.FORGET_PASSWORD;
  static const OTP = _Paths.OTP;
  static const MORE = _Paths.MORE;
  static const PROFILE = _Paths.PROFILE;
  static const POLICY = _Paths.POLICY;
  static const NOTIFICATIONS = _Paths.NOTIFICATIONS;
  static const CONTACT_US = _Paths.CONTACT_US;
  static const CHANGE_PASSWORD = _Paths.CHANGE_PASSWORD;
  static const AUTH = _Paths.AUTH;
  static const DRIVER_PROFILE = _Paths.DRIVER_PROFILE;
  static const DRIVER_ORDERS = _Paths.DRIVER_ORDERS;
  static const DRIVER_BALANCE = _Paths.DRIVER_BALANCE;
  static const COMPANY_PROFILE = _Paths.COMPANY_PROFILE;
  static const COMPANY_ORDERS = _Paths.COMPANY_ORDERS;
  static const COMPANY_HOME = _Paths.COMPANY_HOME;
  static const COMPANY_DRIVERS = _Paths.COMPANY_DRIVERS;
  static const DRIVER_HOME = _Paths.DRIVER_HOME;
  static const COMPANY_PROFILE_EDIT = _Paths.COMPANY_PROFILE_EDIT;
  static const COMPANY_CHANGE_PASSWORD = _Paths.COMPANY_CHANGE_PASSWORD;
  static const COMPANY_CREATE_ORDER = _Paths.COMPANY_CREATE_ORDER;
  static const DRIVER_PROFILE_EDIT = _Paths.DRIVER_PROFILE_EDIT;
  static const DRIVER_CHANGE_PASSWORD = _Paths.DRIVER_CHANGE_PASSWORD;
  static const DRIVER_TRIP = _Paths.DRIVER_TRIP;
  static const ORDER_DETAILS = _Paths.ORDER_DETAILS;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const SPLASH = '/splash';
  static const SETTING = '/setting';
  static const Page = '/page';
  static const LAYOUT = '/layout';
  static const UPDATE_APP = '/update-app';
  static const LOGIN = '/login';
  static const REGISTER = '/register';
  static const FORGET_PASSWORD = '/forget-password';
  static const OTP = '/otp';
  static const MORE = '/more';
  static const PROFILE = '/profile';
  static const POLICY = '/policy';
  static const NOTIFICATIONS = '/notifications';
  static const CONTACT_US = '/contact-us';
  static const CHANGE_PASSWORD = '/change-password';
  static const AUTH = '/auth';
  static const DRIVER_PROFILE = '/driver-profile';
  static const DRIVER_ORDERS = '/driver-orders';
  static const DRIVER_BALANCE = '/driver-balance';
  static const COMPANY_PROFILE = '/company-profile';
  static const COMPANY_ORDERS = '/company-orders';
  static const COMPANY_HOME = '/company-home';
  static const COMPANY_DRIVERS = '/company-drivers';
  static const DRIVER_HOME = '/driver-home';
  static const COMPANY_PROFILE_EDIT = '/company-profile-edit';
  static const COMPANY_CHANGE_PASSWORD = '/company-change-password';
  static const COMPANY_CREATE_ORDER = '/company-create-order';
  static const DRIVER_PROFILE_EDIT = '/driver-profile-edit';
  static const DRIVER_CHANGE_PASSWORD = '/driver-change-password';
  static const DRIVER_TRIP = '/driver-trip';
  static const ORDER_DETAILS = '/order-details';
}
