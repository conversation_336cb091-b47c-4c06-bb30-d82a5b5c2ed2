import 'package:get/get.dart';

import '../modules/CompanyChangePassword/bindings/company_change_password_binding.dart';
import '../modules/CompanyChangePassword/views/company_change_password_view.dart';
import '../modules/CompanyCreateOrder/bindings/company_create_order_binding.dart';
import '../modules/CompanyCreateOrder/views/company_create_order_view.dart';
import '../modules/CompanyProfileEdit/bindings/company_profile_edit_binding.dart';
import '../modules/CompanyProfileEdit/views/company_profile_edit_view.dart';
import '../modules/DriverChangePassword/bindings/driver_change_password_binding.dart';
import '../modules/DriverChangePassword/views/driver_change_password_view.dart';
import '../modules/DriverProfileEdit/bindings/driver_profile_edit_binding.dart';
import '../modules/DriverProfileEdit/views/driver_profile_edit_view.dart';
import '../modules/page/bindings/page_binding.dart';
import '../modules/page/views/page_view.dart';
import '../modules/auth/bindings/auth_binding.dart';
import '../modules/auth/views/auth_view.dart';
import '../modules/changePassword/bindings/change_password_binding.dart';
import '../modules/changePassword/views/change_password_view.dart';
import '../modules/company_drivers/bindings/company_drivers_binding.dart';
import '../modules/company_drivers/views/company_drivers_view.dart';
import '../modules/company_home/bindings/company_home_binding.dart';
import '../modules/company_home/views/company_home_view.dart';
import '../modules/company_orders/bindings/company_orders_binding.dart';
import '../modules/company_orders/views/company_orders_view.dart';
import '../modules/company_profile/bindings/company_profile_binding.dart';
import '../modules/company_profile/views/company_profile_view.dart';
import '../modules/contactUs/bindings/contact_us_binding.dart';
import '../modules/contactUs/views/contact_us_view.dart';
import '../modules/driver_balance/bindings/driver_balance_binding.dart';
import '../modules/driver_balance/views/driver_balance_view.dart';
import '../modules/driver_home/bindings/driver_home_binding.dart';
import '../modules/driver_home/views/driver_home_view.dart';
import '../modules/driver_orders/bindings/driver_orders_binding.dart';
import '../modules/driver_orders/views/driver_orders_view.dart';
import '../modules/driver_profile/bindings/driver_profile_binding.dart';
import '../modules/driver_profile/views/driver_profile_view.dart';
import '../modules/driver_trip/bindings/driver_trip_binding.dart';
import '../modules/driver_trip/views/driver_trip_view.dart';
import '../modules/forgetPassword/bindings/forget_password_binding.dart';
import '../modules/forgetPassword/views/forget_password_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/layout/bindings/layout_binding.dart';
import '../modules/layout/views/layout_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/more/bindings/more_binding.dart';
import '../modules/more/views/more_view.dart';
import '../modules/notifications/bindings/notifications_binding.dart';
import '../modules/notifications/views/notifications_view.dart';
import '../modules/otp/bindings/otp_binding.dart';
import '../modules/otp/views/otp_view.dart';
import '../modules/policy/bindings/policy_binding.dart';
import '../modules/policy/views/policy_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/register/bindings/register_binding.dart';
import '../modules/register/views/register_view.dart';
import '../modules/setting/bindings/setting_binding.dart';
import '../modules/setting/views/setting_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';
import '../modules/updateApp/bindings/update_app_binding.dart';
import '../modules/updateApp/views/update_app_view.dart';
import '../modules/order_details/bindings/order_details_binding.dart';
import '../modules/order_details/views/order_details_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.SETTING,
      page: () => const SettingView(),
      binding: SettingBinding(),
    ),
    GetPage(
      name: _Paths.Page,
      page: () => const PageView(),
      binding: PageBinding(),
    ),
    GetPage(
      name: _Paths.LAYOUT,
      page: () => const LayoutView(),
      binding: LayoutBinding(),
    ),
    GetPage(
      name: _Paths.UPDATE_APP,
      page: () => const UpdateAppView(),
      binding: UpdateAppBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.REGISTER,
      page: () => const RegisterView(),
      binding: RegisterBinding(),
    ),
    GetPage(
      name: _Paths.FORGET_PASSWORD,
      page: () => const ForgetPasswordView(),
      binding: ForgetPasswordBinding(),
    ),
    GetPage(
      name: _Paths.OTP,
      page: () => const OtpView(),
      binding: OtpBinding(),
    ),
    GetPage(
      name: _Paths.MORE,
      page: () => const MoreView(),
      binding: MoreBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: _Paths.POLICY,
      page: () => const PolicyView(),
      binding: PolicyBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATIONS,
      page: () => const NotificationsView(),
      binding: NotificationsBinding(),
    ),
    GetPage(
      name: _Paths.CONTACT_US,
      page: () => const ContactUsView(),
      binding: ContactUsBinding(),
    ),
    GetPage(
      name: _Paths.CHANGE_PASSWORD,
      page: () => const ChangePasswordView(),
      binding: ChangePasswordBinding(),
    ),
    GetPage(
      name: _Paths.AUTH,
      page: () => const AuthView(),
      binding: AuthBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_PROFILE,
      page: () => const DriverProfileView(),
      binding: DriverProfileBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_ORDERS,
      page: () => const DriverOrdersView(),
      binding: DriverOrdersBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_BALANCE,
      page: () => const DriverBalanceView(),
      binding: DriverBalanceBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_PROFILE,
      page: () => const CompanyProfileView(),
      binding: CompanyProfileBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_ORDERS,
      page: () => const CompanyOrdersView(),
      binding: CompanyOrdersBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_HOME,
      page: () => const CompanyHomeView(),
      binding: CompanyHomeBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_DRIVERS,
      page: () => const CompanyDriversView(),
      binding: CompanyDriversBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_HOME,
      page: () => const DriverHomeView(),
      binding: DriverHomeBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_PROFILE_EDIT,
      page: () => const CompanyProfileEditView(),
      binding: CompanyProfileEditBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_CHANGE_PASSWORD,
      page: () => const CompanyChangePasswordView(),
      binding: CompanyChangePasswordBinding(),
    ),
    GetPage(
      name: _Paths.COMPANY_CREATE_ORDER,
      page: () => const CompanyCreateOrderView(),
      binding: CompanyCreateOrderBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_PROFILE_EDIT,
      page: () => const DriverProfileEditView(),
      binding: DriverProfileEditBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_CHANGE_PASSWORD,
      page: () => const DriverChangePasswordView(),
      binding: DriverChangePasswordBinding(),
    ),
    GetPage(
      name: _Paths.DRIVER_TRIP,
      page: () =>  DriverTripView(),
      binding: DriverTripBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_DETAILS,
      page: () => const OrderDetailsView(),
      binding: OrderDetailsBinding(),
    ),
  ];
}
