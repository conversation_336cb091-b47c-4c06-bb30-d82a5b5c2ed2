import 'dart:async';
import 'package:app/app/controllers/global_controller.dart';
import 'package:app/app/utils/helper/const_strings.dart';
import 'package:app/routes_callback.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:toastification/toastification.dart';
import 'package:app_links/app_links.dart';

import 'app/routes/app_pages.dart';
import 'app/utils/helper/echo.dart';
import 'app/utils/theme/app_theme.dart';
import 'app/utils/translations/app_translations.dart';
import 'app/services/location_tracking_service.dart';
import 'injection_container.dart' as inject;
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  //-----------------------------------flutter_downloader-----------------
  WidgetsFlutterBinding.ensureInitialized();

  if (!StringConst.isHuawei && StringConst.enableFirebase && !kIsWeb) {
    await Firebase.initializeApp();
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  // Initialize background location tracking service
  await LocationTrackingService.initializeService();
  await inject.init();

  // Initialize deep link handling
  _initializeDeepLinks();

  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.dark,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  GlobalController globalController = Get.find();

  //Get device theme mode
  // var brightness = SchedulerBinding.instance.window.platformBrightness;
  // bool systemDarkMode = brightness == Brightness.dark;
  // AppPrefs appPrefs = Get.find();
  // bool isDarkMode = appPrefs.getKey(kPrefKeys.darkMode) ?? systemDarkMode;
  // globalController.isDarkMode.value = isDarkMode;

  // await SentryFlutter.init(
  //   (options) {
  //     options.dsn = kDebugMode ? '' : 'https://<EMAIL>/****************';
  //   },
  //   appRunner: () => ,
  // );
  runApp(Obx(
    () {
      globalController.locale.value;
      return ToastificationWrapper(
        child: GetMaterialApp(
          title: "حمولتي",
          enableLog: true,
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
          theme: lightTheme,
          // darkTheme: darkTheme,
          routingCallback: (value) {
            routesCallback(value);
          },
          // themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
          themeMode: ThemeMode.light,
          smartManagement: SmartManagement.keepFactory,
          debugShowCheckedModeBanner: false,
          defaultTransition: Transition.fadeIn,
          transitionDuration: Duration(milliseconds: 250),
          fallbackLocale: globalController.locale.value,
          locale: globalController.locale.value,
          supportedLocales: [
            Locale('ar'),
            Locale('en'),
          ],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          textDirection: globalController.locale.value.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr,
          translations: AppTranslation(),
          builder: (context, child) {
            // return TempScreen();
            double width = MediaQuery.of(context).size.width;
            bool isTablet = width > 600;

            return MediaQuery(
              data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling, boldText: false),
              child: child!,
              // child: ScreenUtilInit(
              //     designSize: isTablet ? Size(744, 1133) : Size(428, 926),
              //     builder: (BuildContext context, Widget? child) {
              //       // bool showDebugArea = false;
              //       globalController.keyboardPadding.value = MediaQuery.of(context).viewInsets.bottom;

              //       return Stack(
              //         children: [
              //           child!,
              //           // Positioned(bottom: 80, left: 0, right: 0, child: SpeechBarWidget()),
              //         ],
              //       );
              //     },
              //     child: child!),
            );
          },
        ),
      );
    },
  ));
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  await Firebase.initializeApp();
  kEcho('FCM Handling a background message ${message.messageId}');
}

void _initializeDeepLinks() {
  final appLinks = AppLinks();

  // Listen to incoming deep links when app is already running
  appLinks.uriLinkStream.listen((Uri uri) {
    _handleDeepLink(uri);
  }, onError: (err) {
    kEcho('Deep link error: $err');
  });

  // Handle deep link when app is launched from a deep link
  appLinks.getInitialLink().then((Uri? uri) {
    if (uri != null) {
      _handleDeepLink(uri);
    }
  });
}

void _handleDeepLink(Uri uri) {
  kEcho('Received deep link: $uri');

  // Parse the deep link
  if (uri.scheme == 'hmolty' && uri.pathSegments.isNotEmpty) {
    final firstSegment = uri.pathSegments[0];

    if (firstSegment == 'order' && uri.pathSegments.length > 1) {
      final orderId = uri.pathSegments[1];

      // Navigate to order details page with the order ID
      Get.toNamed('/order-details', parameters: {'orderId': orderId});
    }
  }
}
