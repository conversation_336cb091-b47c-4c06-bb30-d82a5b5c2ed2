/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@class FPath;

@interface FPruneForest : NSObject

+ (FPruneForest *)empty;

- (BOOL)prunesAnything;
- (BOOL)shouldPruneUnkeptDescendantsAtPath:(FPath *)path;
- (BOOL)shouldKeepPath:(FPath *)path;
- (BOOL)affectsPath:(FPath *)path;
- (FPruneForest *)child:(NSString *)childKey;
- (FPruneForest *)childAtPath:(FPath *)childKey;
- (FPruneForest *)prunePath:(FPath *)path;
- (FPruneForest *)keepPath:(FPath *)path;
- (FPruneForest *)keepAll:(NSSet *)children atPath:(FPath *)path;
- (FPruneForest *)pruneAll:(NSSet *)children atPath:(FPath *)path;

- (void)enumarateKeptNodesUsingBlock:(void (^)(FPath *path))block;

@end
