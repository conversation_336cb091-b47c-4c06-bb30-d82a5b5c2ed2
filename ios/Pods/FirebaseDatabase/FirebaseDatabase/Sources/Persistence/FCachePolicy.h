/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@protocol FCachePolicy <NSObject>

- (BOOL)shouldPruneCacheWithSize:(NSUInteger)cacheSize
          numberOfTrackedQueries:(NSUInteger)numTrackedQueries;
- (BOOL)shouldCheckCacheSize:(NSUInteger)serverUpdatesSinceLastCheck;
- (float)percentOfQueriesToPruneAtOnce;
- (NSUInteger)maxNumberOfQueriesToKeep;

@end

@interface FLRUCachePolicy : NSObject <FCachePolicy>

@property(nonatomic, readonly) NSUInteger maxSize;

- (id)initWithMaxSize:(NSUInteger)maxSize;

@end

@interface FNoCachePolicy : NSObject <FCachePolicy>

+ (FNoCachePolicy *)noCachePolicy;

@end
