/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "FirebaseDatabase/Sources/Persistence/FCachePolicy.h"

@interface FLRUCachePolicy ()

@property(nonatomic, readwrite) NSUInteger maxSize;

@end

static const NSUInteger kFServerUpdatesBetweenCacheSizeChecks = 1000;
static const NSUInteger kFMaxNumberOfPrunableQueriesToKeep = 1000;
static const float kFPercentOfQueriesToPruneAtOnce = 0.2f;

@implementation FLRUCachePolicy

- (id)initWithMaxSize:(NSUInteger)maxSize {
    self = [super init];
    if (self != nil) {
        self->_maxSize = maxSize;
    }
    return self;
}

- (BOOL)shouldPruneCacheWithSize:(NSUInteger)cacheSize
          numberOfTrackedQueries:(NSUInteger)numTrackedQueries {
    return cacheSize > self.maxSize ||
           numTrackedQueries > kFMaxNumberOfPrunableQueriesToKeep;
}

- (BOOL)shouldCheckCacheSize:(NSUInteger)serverUpdatesSinceLastCheck {
    return serverUpdatesSinceLastCheck > kFServerUpdatesBetweenCacheSizeChecks;
}

- (float)percentOfQueriesToPruneAtOnce {
    return kFPercentOfQueriesToPruneAtOnce;
}

- (NSUInteger)maxNumberOfQueriesToKeep {
    return kFMaxNumberOfPrunableQueriesToKeep;
}

@end

@implementation FNoCachePolicy

+ (FNoCachePolicy *)noCachePolicy {
    return [[FNoCachePolicy alloc] init];
}

- (BOOL)shouldPruneCacheWithSize:(NSUInteger)cacheSize
          numberOfTrackedQueries:(NSUInteger)numTrackedQueries {
    return NO;
}

- (BOOL)shouldCheckCacheSize:(NSUInteger)serverUpdatesSinceLastCheck {
    return NO;
}

- (float)percentOfQueriesToPruneAtOnce {
    return 0;
}

- (NSUInteger)maxNumberOfQueriesToKeep {
    return NSUIntegerMax;
}

@end
