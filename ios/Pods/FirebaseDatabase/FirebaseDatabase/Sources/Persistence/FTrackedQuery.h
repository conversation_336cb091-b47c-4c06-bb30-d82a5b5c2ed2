/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@class FQuerySpec;

@interface FTrackedQuery : NSObject

@property(nonatomic, readonly) NSUInteger queryId;
@property(nonatomic, strong, readonly) FQuerySpec *query;
@property(nonatomic, readonly) NSTimeInterval lastUse;
@property(nonatomic, readonly) BOOL isComplete;
@property(nonatomic, readonly) BOOL isActive;

- (id)initWithId:(NSUInteger)queryId
           query:(FQuerySpec *)query
         lastUse:(NSTimeInterval)lastUse
        isActive:(BOOL)isActive;
- (id)initWithId:(NSUInteger)queryId
           query:(FQuerySpec *)query
         lastUse:(NSTimeInterval)lastUse
        isActive:(BOOL)isActive
      isComplete:(BOOL)isComplete;

- (FTrackedQuery *)updateLastUse:(NSTimeInterval)lastUse;
- (FTrackedQuery *)setComplete;
- (FTrackedQuery *)setActiveState:(BOOL)isActive;

@end
