/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "FirebaseDatabase/Sources/Utilities/Tuples/FTupleTransaction.h"
#import "FirebaseDatabase/Sources/Utilities/FUtilities.h"

@interface FTupleTransaction ()

@property(nonatomic, strong) NSString *abortStatus;
@property(nonatomic, strong) NSString *abortReason;

@end

@implementation FTupleTransaction

- (void)setAbortStatus:(NSString *)abortStatus reason:(NSString *)reason {
    self.abortStatus = abortStatus;
    self.abortReason = reason;
}

- (NSError *)abortError {
    return (self.abortStatus != nil)
               ? [FUtilities errorForStatus:self.abortStatus
                                  andReason:self.abortReason]
               : nil;
}

@end
