/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@class FPath;
@class FCompoundWrite;
@protocol FNode;

@interface FWriteRecord : NSObject

- initWithPath:(FPath *)path
     overwrite:(id<FNode>)overwrite
       writeId:(NSInteger)writeId
       visible:(BOOL)isVisible;
- initWithPath:(FPath *)path
         merge:(FCompoundWrite *)merge
       writeId:(NSInteger)writeId;

@property(nonatomic, readonly) NSInteger writeId;
@property(nonatomic, strong, readonly) FPath *path;
@property(nonatomic, strong, readonly) id<FNode> overwrite;
/**
 * Maps NSString -> id<FNode>
 */
@property(nonatomic, strong, readonly) FCompoundWrite *merge;
@property(nonatomic, readonly) BOOL visible;

- (BOOL)isMerge;
- (BOOL)isOverwrite;

@end
