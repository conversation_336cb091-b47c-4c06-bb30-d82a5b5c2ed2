/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

@class FQueryParams;

@interface FOperationSource : NSObject

@property(nonatomic, readonly) BOOL fromUser;
@property(nonatomic, readonly) BOOL fromServer;
@property(nonatomic, readonly) BOOL isTagged;
@property(nonatomic, strong, readonly) FQueryParams *queryParams;

- initWithFromUser:(BOOL)isFromUser
        fromServer:(BOOL)isFromServer
       queryParams:(FQueryParams *)params
            tagged:(BOOL)isTagged;

+ (FOperationSource *)userInstance;
+ (FOperationSource *)serverInstance;
+ (FOperationSource *)forServerTaggedQuery:(FQueryParams *)params;

@end
