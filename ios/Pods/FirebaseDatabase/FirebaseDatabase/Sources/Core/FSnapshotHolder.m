/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "FirebaseDatabase/Sources/Core/FSnapshotHolder.h"
#import "FirebaseDatabase/Sources/Snapshot/FEmptyNode.h"

@interface FSnapshotHolder ()

@end

@implementation FSnapshotHolder

@synthesize rootNode;

- (id)init {
    self = [super init];
    if (self) {
        self.rootNode = [FEmptyNode emptyNode];
    }
    return self;
}

- (id<FNode>)getNode:(FPath *)path {
    return [self.rootNode getChild:path];
}

- (void)updateSnapshot:(FPath *)path
       withNewSnapshot:(id<FNode>)newSnapshotNode {
    self.rootNode = [self.rootNode updateChild:path
                                  withNewChild:newSnapshotNode];
}

@end
