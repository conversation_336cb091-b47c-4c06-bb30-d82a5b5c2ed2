/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "FirebaseDatabase/Sources/Api/Private/FTypedefs_Private.h"
#import "FirebaseDatabase/Sources/Core/Utilities/FPath.h"
#import <Foundation/Foundation.h>

@protocol FIndex;

@protocol FNode <NSObject>

- (BOOL)isLeafNode;
- (id<FNode>)getPriority;
- (id<FNode>)updatePriority:(id<FNode>)priority;
- (id<FNode>)getImmediateChild:(NSString *)childKey;
- (id<FNode>)getChild:(FPath *)path;
- (NSString *)predecessorChildKey:(NSString *)childKey;
- (id<FNode>)updateImmediateChild:(NSString *)childKey
                     withNewChild:(id<FNode>)newChildNode;
- (id<FNode>)updateChild:(FPath *)path withNewChild:(id<FNode>)newChildNode;
- (BOOL)hasChild:(NSString *)childKey;
- (BOOL)isEmpty;
- (int)numChildren;
- (id)val;
- (id)valForExport:(BOOL)exp;
- (NSString *)dataHash;
- (NSComparisonResult)compare:(id<FNode>)other;
- (BOOL)isEqual:(id<FNode>)other;
- (void)enumerateChildrenUsingBlock:(void (^)(NSString *key, id<FNode> node,
                                              BOOL *stop))block;
- (void)enumerateChildrenReverse:(BOOL)reverse
                      usingBlock:(void (^)(NSString *key, id<FNode> node,
                                           BOOL *stop))block;

- (NSEnumerator *)childEnumerator;

@end
