{"ABIRoot": {"kind": "Root", "name": "TopLevel", "printedName": "TopLevel", "children": [{"kind": "Import", "name": "StoreKit", "printedName": "StoreKit", "declKind": "Import", "moduleName": "FirebaseAnalytics"}, {"kind": "Import", "name": "SwiftUI", "printedName": "SwiftUI", "declKind": "Import", "moduleName": "FirebaseAnalytics"}, {"kind": "TypeDecl", "name": "Analytics", "printedName": "Analytics", "children": [{"kind": "Function", "name": "logTransaction", "printedName": "logTransaction(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Transaction", "printedName": "StoreKit.Transaction", "usr": "s:8StoreKit11TransactionV"}], "declKind": "Func", "usr": "s:So12FIRAnalyticsC17FirebaseAnalyticsE14logTransactionyy8StoreKit0E0VFZ", "mangledName": "$sSo12FIRAnalyticsC17FirebaseAnalyticsE14logTransactionyy8StoreKit0E0VFZ", "moduleName": "FirebaseAnalytics", "static": true, "declAttributes": ["Final", "AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)FIRAnalytics", "moduleName": "FirebaseAnalytics", "isOpen": true, "objc_name": "FIRAnalytics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "View", "printedName": "View", "children": [{"kind": "Function", "name": "analyticsScreen", "printedName": "analyticsScreen(name:class:extraParameters:)", "children": [{"kind": "TypeNominal", "name": "ModifiedContent", "printedName": "SwiftUI.ModifiedContent<τ_0_0, FirebaseAnalytics.LoggedAnalyticsModifier>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "LoggedAnalyticsModifier", "printedName": "FirebaseAnalytics.LoggedAnalyticsModifier", "usr": "s:17FirebaseAnalytics06LoggedB8ModifierV"}], "usr": "s:7SwiftUI15ModifiedContentV"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "hasDefaultArg": true, "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Dictionary", "printedName": "[Swift.String : Any]", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "hasDefaultArg": true, "usr": "s:SD"}], "declKind": "Func", "usr": "s:7SwiftUI4ViewP17FirebaseAnalyticsE15analyticsScreen4name5class15extraParametersQrSS_SSSDySSypGtF", "mangledName": "$s7SwiftUI4ViewP17FirebaseAnalyticsE15analyticsScreen4name5class15extraParametersQrSS_SSSDySSypGtF", "moduleName": "FirebaseAnalytics", "genericSig": "<τ_0_0 where τ_0_0 : SwiftUI.View>", "sugared_genericSig": "<Self where Self : SwiftUI.View>", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "s:7SwiftUI4ViewP", "mangledName": "$s7SwiftUI4ViewP", "moduleName": "SwiftUI", "genericSig": "<τ_0_0.Body : SwiftUI.View>", "sugared_genericSig": "<Self.Body : SwiftUI.View>", "intro_Macosx": "10.15", "intro_iOS": "13.0", "intro_tvOS": "13.0", "intro_watchOS": "6.0", "declAttributes": ["TypeEraser", "Available", "Available", "Available", "Available"], "isExternal": true}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1259, "length": 5, "value": "false"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "<PERSON>olean<PERSON>iter<PERSON>", "offset": 1297, "length": 5, "value": "false"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "IntegerLiteral", "offset": 2523, "length": 1, "value": "0"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "IntegerLiteral", "offset": 2564, "length": 1, "value": "1"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "IntegerLiteral", "offset": 2607, "length": 1, "value": "2"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "IntegerLiteral", "offset": 2651, "length": 1, "value": "3"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+StoreKit.swift", "kind": "IntegerLiteral", "offset": 2683, "length": 1, "value": "0"}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+SwiftUI.swift", "kind": "StringLiteral", "offset": 2654, "length": 6, "value": "\"View\""}, {"filePath": "/Volumes/google/src/cloud/hantran/m152/google3/googlemac/iPhone/Firebase/Analytics/Sources/Swift/Analytics+SwiftUI.swift", "kind": "Dictionary", "offset": 2701, "length": 3, "value": "[]"}]}