<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		4TWkcATxuIU+5C+hG4fT+ROVRUs=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		0nWXABx/HRqcUMkqMxRdJuHxqQU=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		WX6aoBMqQwQzsDvLeyE0OgFVPrw=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		3M+pYToQyErPKuPVG6i1Cq45UGg=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		NAvpCrv1M1YGI6Vin6eL42cx/3s=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		BHIT/FyHupjfAkf+upImM8fRLqU=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		NAvpCrv1M1YGI6Vin6eL42cx/3s=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<data>
		hLMA9SEHbpnzoKCc9HUHc1uhKak=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-macabi.swiftsourceinfo</key>
		<data>
		EhtqX7DzKoQW0iSi3IUTGK5b37Q=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-macabi.swiftsourceinfo</key>
		<data>
		JLsXBdIWPoRNn3Kb2eMFEdHVdRA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		4gR6vGh9NGCOc8NqbJitBi4PYMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		qhoelsD3haNmZEE9NM8xvtK9mEY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		4gR6vGh9NGCOc8NqbJitBi4PYMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		R1LVVji18GwfW9nhhjO5aFGB1+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		/8RdTbctZKCA1/OIwmZLl2oWnCk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		R1LVVji18GwfW9nhhjO5aFGB1+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		svXxv0/OGVjQmF1D+z3z0w5TRm0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		5lkjmj4xdvZvK3gmirF/U6+EOHk=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		etmbf5xWw3wEKGL3IWC/ow/wF+M=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		FhnJN0FjXUhPYpTZbg7lfTzY4iM=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		G1rPKi+yEapomahwUr5E85athPA=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		hZUm+6Yon0B4K7L8GD1ScdCY3t0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		JeXYTVdQMygRSZdbbP8kc/pgjAI=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		hZUm+6Yon0B4K7L8GD1ScdCY3t0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		eO9rriExhXQgyXhchyORV3h91L4=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		mjATLkNPULjHjLv3ARUXQ7doOSY=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		eO9rriExhXQgyXhchyORV3h91L4=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<data>
		k2YgKDybd3CbLlkKKDkufS9bf5M=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		sXk7jWhcfsvb8eJb7/NDl2Dy2RQ=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<data>
		tDXAvUd1DT/hf+gGhRWBN9f6++k=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<data>
		yO6TP0ibVAPGnKhw/PP4xRl7xRU=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<data>
		ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<data>
		1zu68JlIn8zDFnfRoKMDRMs6j5I=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<data>
		ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<data>
		TztLGK3InUf3F4NjuC+lzdaWf50=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<data>
		ByKNCJ6aB6LHj+qNk+ZX3YQ3LWM=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<data>
		TztLGK3InUf3F4NjuC+lzdaWf50=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		jWZ+azdgXQdH9z56z25NptmWlq4=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		oYHe0jq2/d647hnie1kXM5yTzG0=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		6KKSHK++Blij52zq9pAdBDlRLbQ=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		0nWXABx/HRqcUMkqMxRdJuHxqQU=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		QpGj6BfYgPdzaxBNh2oz0+kEaqg=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos.swiftsourceinfo</key>
		<data>
		b0d6xVfiF71u0Dj4E4tbrPn+9wc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.private.swiftinterface</key>
		<data>
		Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftdoc</key>
		<data>
		ICm3QgbA9GCrU8RrR6izKGRnrLA=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftinterface</key>
		<data>
		Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		ScPpK9PZ1K2h4MEIEhGdimAqFGg=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		q9RykUmARxGpwWkmA+HY/h9DPA0=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos-simulator.swiftsourceinfo</key>
		<data>
		WC+E5oT39/Vu6ecgffiaR00kGys=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-tvos-simulator.swiftsourceinfo</key>
		<data>
		xw34VmRefYpxywjZs0NQB+4xvz8=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.private.swiftinterface</key>
		<data>
		+EoKjIoaiJH4HuBxFOAR+N6qm/A=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftdoc</key>
		<data>
		kEpmMJ+EwdoypGxC9M4fHrfAYic=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftinterface</key>
		<data>
		+EoKjIoaiJH4HuBxFOAR+N6qm/A=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.abi.json</key>
		<data>
		66KHnyUOUpeHtfGUA412UpsRmhw=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.private.swiftinterface</key>
		<data>
		KthVIak11hjuXvIlm095GJUwL1M=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftdoc</key>
		<data>
		65HbWdHXydClg5RHbdZeeWhc8us=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftinterface</key>
		<data>
		KthVIak11hjuXvIlm095GJUwL1M=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			4TWkcATxuIU+5C+hG4fT+ROVRUs=
			</data>
			<key>hash2</key>
			<data>
			8/9oZKg7xVvmNpY5bHq1YNlySb2+UfgrbO0rakY0aKw=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nWXABx/HRqcUMkqMxRdJuHxqQU=
			</data>
			<key>hash2</key>
			<data>
			DmmG7v+JGyWpcKQl1BeGd8rqz4HV3b+PToUt+aM45bY=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			WX6aoBMqQwQzsDvLeyE0OgFVPrw=
			</data>
			<key>hash2</key>
			<data>
			6HZwAdwIcLgdAYbXj2Rdo8hxuIk/yBzbvcA/jwYk7VE=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			3M+pYToQyErPKuPVG6i1Cq45UGg=
			</data>
			<key>hash2</key>
			<data>
			gDXpDQ46UaffVyDDJojjT9cq4VI6EG0nsDQq7vP4Osg=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			NAvpCrv1M1YGI6Vin6eL42cx/3s=
			</data>
			<key>hash2</key>
			<data>
			w4kvFgyfxWYtCjCy0BYHS05P0r5XAws8V9Mc4rKyOaQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			BHIT/FyHupjfAkf+upImM8fRLqU=
			</data>
			<key>hash2</key>
			<data>
			VD+vnhQT/tzGLf1nYQU+/onj2SvCzxwQtgst449XRlc=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			NAvpCrv1M1YGI6Vin6eL42cx/3s=
			</data>
			<key>hash2</key>
			<data>
			w4kvFgyfxWYtCjCy0BYHS05P0r5XAws8V9Mc4rKyOaQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FirebaseAnalytics</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			hLMA9SEHbpnzoKCc9HUHc1uhKak=
			</data>
			<key>hash2</key>
			<data>
			5NXk7T4Y6W/XNKWU0Djgq2+7hF3KSBus3PucXubv7bM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-macabi.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			EhtqX7DzKoQW0iSi3IUTGK5b37Q=
			</data>
			<key>hash2</key>
			<data>
			EB/Dw4T1UMGv1jgPZ1eCWm7fdXPIo8yT0CkHETyq3mU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-macabi.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			JLsXBdIWPoRNn3Kb2eMFEdHVdRA=
			</data>
			<key>hash2</key>
			<data>
			jXBXaBJl9zxQZXHt4XmxiSmutlJNIM/Y2nF+nFirDLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			4gR6vGh9NGCOc8NqbJitBi4PYMc=
			</data>
			<key>hash2</key>
			<data>
			sBsGCsKt/qVMlUX2JkA5hB1ko0klhw7cNGdqgUlwGW4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			qhoelsD3haNmZEE9NM8xvtK9mEY=
			</data>
			<key>hash2</key>
			<data>
			ORG2lGLtqMk9O/g5fSRsgM0vkRuCeApJ2rKBJUOAyQY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			4gR6vGh9NGCOc8NqbJitBi4PYMc=
			</data>
			<key>hash2</key>
			<data>
			sBsGCsKt/qVMlUX2JkA5hB1ko0klhw7cNGdqgUlwGW4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			R1LVVji18GwfW9nhhjO5aFGB1+k=
			</data>
			<key>hash2</key>
			<data>
			8sleA8PWyFsmq4UfIdcmC5GkHeUcPvp/kAytxpjR9N0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			/8RdTbctZKCA1/OIwmZLl2oWnCk=
			</data>
			<key>hash2</key>
			<data>
			zgcFlEjAeUx/3gKqZtb0j32k5wK3ScunLgYWrpCZ8YY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			R1LVVji18GwfW9nhhjO5aFGB1+k=
			</data>
			<key>hash2</key>
			<data>
			8sleA8PWyFsmq4UfIdcmC5GkHeUcPvp/kAytxpjR9N0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			svXxv0/OGVjQmF1D+z3z0w5TRm0=
			</data>
			<key>hash2</key>
			<data>
			Q9LZdmVWVRUP3q3lLdhUHRJR4KHk7xMtSOrKMCdIp+o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			5lkjmj4xdvZvK3gmirF/U6+EOHk=
			</data>
			<key>hash2</key>
			<data>
			ov7GDmKsTwBrxyGJDDKZsskGgKQ/iTjYMmwtXYiLLhg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			etmbf5xWw3wEKGL3IWC/ow/wF+M=
			</data>
			<key>hash2</key>
			<data>
			kH3fU+ZuMtEki514m7Cc6ofP/bWS1iaUZyQ+karEG6c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			FhnJN0FjXUhPYpTZbg7lfTzY4iM=
			</data>
			<key>hash2</key>
			<data>
			9XmCR5irL/8jhiloXGrknFOIXREzqF5D7zOusygjD+M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			G1rPKi+yEapomahwUr5E85athPA=
			</data>
			<key>hash2</key>
			<data>
			O/FxrYsc5ddEnVxgrppEhKMlUD5uDlwSVru+JzigV/Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			hZUm+6Yon0B4K7L8GD1ScdCY3t0=
			</data>
			<key>hash2</key>
			<data>
			QzIaVFFMO8tYIzl4o3nbVBhW2fuNtgOmkSNeDMOfOfA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			JeXYTVdQMygRSZdbbP8kc/pgjAI=
			</data>
			<key>hash2</key>
			<data>
			R2Pt3A/l7SQ4n2I93xw+Ffz7yGbF0Q7HJd4fqxMgV10=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			hZUm+6Yon0B4K7L8GD1ScdCY3t0=
			</data>
			<key>hash2</key>
			<data>
			QzIaVFFMO8tYIzl4o3nbVBhW2fuNtgOmkSNeDMOfOfA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			eO9rriExhXQgyXhchyORV3h91L4=
			</data>
			<key>hash2</key>
			<data>
			lt9Vh+N0wzNo3JZuavlytTxaN9X/rwKiQlcn7Xt6DU4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			mjATLkNPULjHjLv3ARUXQ7doOSY=
			</data>
			<key>hash2</key>
			<data>
			gcmxRgiXlWBZMSGlxd3/aPJfzJWomCwGizfkHXKvofA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			eO9rriExhXQgyXhchyORV3h91L4=
			</data>
			<key>hash2</key>
			<data>
			lt9Vh+N0wzNo3JZuavlytTxaN9X/rwKiQlcn7Xt6DU4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FirebaseAnalytics</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			k2YgKDybd3CbLlkKKDkufS9bf5M=
			</data>
			<key>hash2</key>
			<data>
			bvTqmJfY3GA8URECdr8W5Etny9hghCS8GDdFeB7B0/s=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			sXk7jWhcfsvb8eJb7/NDl2Dy2RQ=
			</data>
			<key>hash2</key>
			<data>
			E6EdPyZp5cQbOObE0CPO9/R+QvTIYlDIOHvuzam/9H8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			tDXAvUd1DT/hf+gGhRWBN9f6++k=
			</data>
			<key>hash2</key>
			<data>
			QzCJQ72/JCPFZASJhWt7lF4KSL9gQCJAHOBGZixXkws=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			yO6TP0ibVAPGnKhw/PP4xRl7xRU=
			</data>
			<key>hash2</key>
			<data>
			5IoLEq5Ozo113OVhVe9+W2gjx/eVozbIGXB2vjZ+Vw8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
			</data>
			<key>hash2</key>
			<data>
			65tpMWBKS1d0GpEhBByvoQ0+KStlRWp8GF8K7dzViE8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			1zu68JlIn8zDFnfRoKMDRMs6j5I=
			</data>
			<key>hash2</key>
			<data>
			5q8dF0bgCDYfD0KnwHIw+wuj/GogPeqMVp2cx9uulKg=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
			</data>
			<key>hash2</key>
			<data>
			65tpMWBKS1d0GpEhBByvoQ0+KStlRWp8GF8K7dzViE8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			TztLGK3InUf3F4NjuC+lzdaWf50=
			</data>
			<key>hash2</key>
			<data>
			nSnXCeosOPuMgd2b41zKfQBUM22ZloRdAj9hyRYK8wk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			ByKNCJ6aB6LHj+qNk+ZX3YQ3LWM=
			</data>
			<key>hash2</key>
			<data>
			7L1iy8hHgpzEAPYgxmkdsz/w6JO8FzKST9eY6Hz9nQU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			TztLGK3InUf3F4NjuC+lzdaWf50=
			</data>
			<key>hash2</key>
			<data>
			nSnXCeosOPuMgd2b41zKfQBUM22ZloRdAj9hyRYK8wk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			jWZ+azdgXQdH9z56z25NptmWlq4=
			</data>
			<key>hash2</key>
			<data>
			e4a41Axjw7BAywMms/GuOcqrdwPIVYuMRsPznqQS0X8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			oYHe0jq2/d647hnie1kXM5yTzG0=
			</data>
			<key>hash2</key>
			<data>
			GumuYQPnyvV63aI4FIGdxXwspWQEKX6j6bgTpwkINkY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			6KKSHK++Blij52zq9pAdBDlRLbQ=
			</data>
			<key>hash2</key>
			<data>
			8WL/5BNllU4afHaeflRtaPrbFHtVzSRBRgv1EoW2us8=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nWXABx/HRqcUMkqMxRdJuHxqQU=
			</data>
			<key>hash2</key>
			<data>
			DmmG7v+JGyWpcKQl1BeGd8rqz4HV3b+PToUt+aM45bY=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			QpGj6BfYgPdzaxBNh2oz0+kEaqg=
			</data>
			<key>hash2</key>
			<data>
			owmnZRQqsuSDRrqYBYESImL/Zib1MN9AhCETv8GjbXQ=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			b0d6xVfiF71u0Dj4E4tbrPn+9wc=
			</data>
			<key>hash2</key>
			<data>
			QyXu9XGPHG0qkF86498YTbqqlYBOG924tN0z82f2Ghk=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
			</data>
			<key>hash2</key>
			<data>
			EsiBmwD4KG8UOQJ9SEWfB/O1mnTXHmVIEyAeH2JVApE=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			ICm3QgbA9GCrU8RrR6izKGRnrLA=
			</data>
			<key>hash2</key>
			<data>
			Wd9mR8J/cQBEqDuyZ1x32/TWxOvg35osz6P9hEE4nck=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
			</data>
			<key>hash2</key>
			<data>
			EsiBmwD4KG8UOQJ9SEWfB/O1mnTXHmVIEyAeH2JVApE=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			ScPpK9PZ1K2h4MEIEhGdimAqFGg=
			</data>
			<key>hash2</key>
			<data>
			e/mMv3A45/g5+5Vo4fkyav4kg//pyK1lQkcRmEuS5Io=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			q9RykUmARxGpwWkmA+HY/h9DPA0=
			</data>
			<key>hash2</key>
			<data>
			aql5HABSrVkuBeSRm5uQO6NG2o+sxbJr7jcw1WpUlEU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			WC+E5oT39/Vu6ecgffiaR00kGys=
			</data>
			<key>hash2</key>
			<data>
			w7B16PsLn7E+3+D/saa8rPXSOTv0IINGbNPPHPgRZ+0=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-tvos-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			xw34VmRefYpxywjZs0NQB+4xvz8=
			</data>
			<key>hash2</key>
			<data>
			OYRUNqHkG5UkTTHQx4K7PO4J+oeeC5X61EMppp0tTow=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			+EoKjIoaiJH4HuBxFOAR+N6qm/A=
			</data>
			<key>hash2</key>
			<data>
			DoD1OSJHno0bNy0osPn/laVPoBAxHGWB789dQSNEMkI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			kEpmMJ+EwdoypGxC9M4fHrfAYic=
			</data>
			<key>hash2</key>
			<data>
			kgbprKMKWtu9nlXnOTVjHVY0TAgVDbK5pK/G+QpaymE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			+EoKjIoaiJH4HuBxFOAR+N6qm/A=
			</data>
			<key>hash2</key>
			<data>
			DoD1OSJHno0bNy0osPn/laVPoBAxHGWB789dQSNEMkI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			66KHnyUOUpeHtfGUA412UpsRmhw=
			</data>
			<key>hash2</key>
			<data>
			C4diFGoEyBmGjFO8mP/P4nf/3oZuROOsFMcNqQ/DHE8=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			KthVIak11hjuXvIlm095GJUwL1M=
			</data>
			<key>hash2</key>
			<data>
			MwVZoM3SRxYLhYEsI316k3d6aaaJ31Xne1MXyoX0sFs=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			65HbWdHXydClg5RHbdZeeWhc8us=
			</data>
			<key>hash2</key>
			<data>
			HXWYfxPhG1Lkni6+8MhUCQGNPmfg72yH2N6CVQbWuuI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			KthVIak11hjuXvIlm095GJUwL1M=
			</data>
			<key>hash2</key>
			<data>
			MwVZoM3SRxYLhYEsI316k3d6aaaJ31Xne1MXyoX0sFs=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
