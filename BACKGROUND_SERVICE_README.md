# Background Location Tracking Service

This document explains the implementation of the background location tracking service for the driver trip functionality.

## Overview

The background location tracking service allows the app to continue tracking the driver's location and sending updates to the server even when the app is in the background or the screen is locked.

## Components

### 1. LocationTrackingService (`lib/app/services/location_tracking_service.dart`)

This is the main service class that handles:
- Background service initialization
- Starting and stopping location tracking
- Handling location updates in the background
- Sending location data to the server
- Managing foreground notifications

### 2. Updated DriverTripController

The controller has been enhanced with:
- Background location permission handling
- Integration with the background service
- Service state management
- UI updates from background service events

### 3. Platform Configuration

#### Android (`android/app/src/main/AndroidManifest.xml`)
- Added background location permission
- Added foreground service permissions
- Configured background service

#### iOS (`ios/Runner/Info.plist`)
- Added background location usage descriptions
- Configured background modes for location tracking

## Features

### Foreground Service
- Shows a persistent notification when tracking is active
- Prevents the system from killing the location tracking
- Updates notification with current location info

### Background Location Tracking
- Continues tracking when app is backgrounded
- Sends location updates to server automatically
- Handles network errors gracefully

### Permission Management
- Requests appropriate location permissions
- Handles background location permission separately
- Shows user-friendly error messages

### UI Integration
- Shows background service status indicator
- Real-time updates from background service
- Seamless transition between foreground and background tracking

## Usage

### Starting Background Tracking
When the driver trip page is opened:
1. Location permissions are checked
2. Background location permission is requested
3. Background service is started automatically
4. Foreground location stream is also started for real-time UI updates

### Stopping Background Tracking
When the driver trip page is closed:
1. Background service is stopped
2. Foreground location stream is cancelled
3. Notification is removed

## Technical Details

### Location Settings
- **Accuracy**: High accuracy GPS
- **Distance Filter**: 10 meters (updates every 10 meters of movement)
- **Time Limit**: 30 seconds timeout for location requests

### Notification Channel
- **Channel ID**: `location_tracking_channel`
- **Importance**: Low (no sound or vibration)
- **Ongoing**: True (persistent notification)

### Data Storage
- Order data is stored in GetStorage for background access
- Service state is managed through reactive variables

## Error Handling

The service handles various error scenarios:
- Location permission denied
- Location service disabled
- Network errors when sending data
- Service initialization failures

## Testing

To test the background service:
1. Open the driver trip page
2. Check that the "Background Tracking" indicator shows green
3. Put the app in background or lock the screen
4. Verify that location updates continue to be sent to the server
5. Check the notification shows current location updates

## Dependencies

- `flutter_background_service: ^5.0.10`
- `flutter_local_notifications: ^17.2.3`
- `geolocator: ^14.0.0`
- `permission_handler: ^11.2.0`

## Notes

- The background service will automatically stop if the app is force-closed
- iOS has stricter background execution limits than Android
- The service respects battery optimization settings on Android
- Location accuracy may be reduced in background mode on some devices
